"""
异常感知知识追踪流程的各个阶段

将full_pipeline.py中的各个阶段拆分为独立的类，提高代码的模块化和可维护性。
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Tuple, Optional, Any, List
import json
from tqdm import tqdm

from DTransformer.data import KTData
from DTransformer.model import DTransformer

from .detector import CausalAnomalyDetector
from .model import AnomalyAwareDTransformer
from .trainer import KnowledgeTracingTrainer
from .unified_trainer import UnifiedAnomalyTrainer
from .evaluator import ComparisonEvaluator, plot_training_curves

from .enhanced_anomaly_aware_trainer import EnhancedAnomalyAwareTrainer

class PipelineStage:
    """流程阶段基类"""

    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化流程阶段

        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir

    def execute(self, *args, **kwargs) -> Any:
        """执行阶段"""
        raise NotImplementedError("子类必须实现execute方法")


class BaselineModelTrainer(PipelineStage):
    """基线DTransformer模型训练阶段"""

    def __init__(self, args: Any, device: str, output_dir: str):
        super().__init__(args, device, output_dir)
        self.save_dir = os.path.join(output_dir, 'baseline')
        os.makedirs(self.save_dir, exist_ok=True)

    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData) -> str:
        """
        训练基线DTransformer模型

        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据

        Returns:
            str: 保存的模型路径
        """
        print("\n" + "="*60)
        print("PHASE 1: Training Baseline DTransformer")
        print("="*60)

        # 创建模型
        model = DTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window
        )

        # 训练器
        trainer = KnowledgeTracingTrainer(
            model=model,
            device=self.device,
            save_dir=self.save_dir,
            patience=self.args.patience
        )

        # 训练
        baseline_metrics = trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=self.args.kt_epochs,
            learning_rate=self.args.learning_rate,
            use_cl=self.args.use_cl
        )

        print(f"\nBaseline training completed!")
        print(f"Best AUC: {baseline_metrics['auc']:.4f}")

        return os.path.join(self.save_dir, 'best_model.pt')


class AnomalyDetectorTrainer(PipelineStage):
    """异常检测器训练阶段"""

    def __init__(self, args: Any, device: str, output_dir: str):
        super().__init__(args, device, output_dir)
        self.save_dir = os.path.join(output_dir, 'detector')
        os.makedirs(self.save_dir, exist_ok=True)

    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData) -> str:
        """
        训练异常检测器

        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据

        Returns:
            str: 保存的模型路径
        """
        print("\n" + "="*60)
        print("PHASE 2: Training Anomaly Detector")
        print("="*60)

        # 创建模型
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.detector_d_model,
            n_heads=self.args.detector_n_heads,
            n_layers=self.args.detector_n_layers,
            dropout=self.args.detector_dropout,
            window_size=self.args.window_size
        )

        print(f"🧠 模型参数: {sum(p.numel() for p in detector.parameters()):,}")

        # 选择训练策略
        strategy = getattr(self.args, 'training_strategy', 'basic')

        # 创建统一训练器
        trainer = UnifiedAnomalyTrainer(
            model=detector,
            device=self.device,
            save_dir=self.save_dir,
            patience=self.args.detector_patience,
            strategy=strategy
        )

        # 如果启用了课程学习，传递给训练策略
        if hasattr(self.args, 'use_curriculum') and self.args.use_curriculum and hasattr(trainer, 'strategy') and hasattr(trainer.strategy, 'use_curriculum'):
            trainer.strategy.use_curriculum = True
            print("✓ 课程学习已启用")

        # 显示策略信息
        strategy_info = trainer.get_strategy_info()
        print(f"\n📋 {strategy_info['description']}")

        # 训练
        detector_metrics = trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=self.args.detector_epochs,
            learning_rate=self.args.detector_lr,
            anomaly_ratio=self.args.anomaly_ratio,
            optimize_for=self.args.optimize_for
        )

        print(f"\n🎉 检测器训练完成!")
        print(f"📊 最终结果:")
        print(f"  F1 Score: {detector_metrics['f1_score']:.4f}")
        print(f"  AUC-ROC: {detector_metrics['auc_roc']:.4f}")
        print(f"  Recall: {detector_metrics['recall']:.4f}")
        print(f"  Precision: {detector_metrics['precision']:.4f}")

        # 绘制训练曲线
        plot_training_curves(trainer.history, self.save_dir)

        return os.path.join(self.save_dir, 'best_model.pt')


class AnomalyAwareModelTrainer(PipelineStage):
    """异常感知的知识追踪模型训练阶段"""

    def __init__(self, args: Any, device: str, output_dir: str):
        super().__init__(args, device, output_dir)
        self.save_dir = os.path.join(output_dir, 'anomaly_aware')
        os.makedirs(self.save_dir, exist_ok=True)

    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData, detector_path: str) -> str:
        """
        训练异常感知的知识追踪模型

        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据
            detector_path: 异常检测器模型路径

        Returns:
            str: 保存的模型路径
        """
        print("\n" + "="*60)
        print("PHASE 3: Training Anomaly-Aware DTransformer")
        print("="*60)


    # 使用增强训练器


        # 加载异常检测器
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.detector_d_model,
            n_heads=self.args.detector_n_heads,
            n_layers=self.args.detector_n_layers,
            dropout=self.args.detector_dropout,
            window_size=self.args.window_size
        )

        # 正确加载checkpoint
        checkpoint = torch.load(detector_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            detector.load_state_dict(checkpoint['model_state_dict'])
        else:
            detector.load_state_dict(checkpoint)
        detector.eval()

        # 创建异常感知模型
        model = AnomalyAwareDTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window,
            anomaly_detector=detector,
            anomaly_weight=self.args.anomaly_weight
        )

        trainer = EnhancedAnomalyAwareTrainer(
            model=model,
            detector=detector,  # 传入检测器
            device=self.device,
            save_dir=self.save_dir,
            patience=self.args.patience,
            use_curriculum=True,  # 启用课程学习
            use_game_theory=True  # 启用博弈论
        )

        # 检查是否从检查点恢复
        start_epoch = 1
        if hasattr(self.args, 'resume_from_checkpoint') and self.args.resume_from_checkpoint:
            print(f"Loading checkpoint from: {self.args.resume_from_checkpoint}")
            checkpoint = torch.load(self.args.resume_from_checkpoint, map_location=self.device)

            # 加载模型状态
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print("✓ Model state loaded from checkpoint")
            else:
                model.load_state_dict(checkpoint)
                print("✓ Model state loaded from checkpoint (direct format)")

            # 设置起始轮次
            if hasattr(self.args, 'start_epoch') and self.args.start_epoch:
                start_epoch = self.args.start_epoch
                print(f"✓ Will resume training from epoch {start_epoch}")

        # 训练
        anomaly_metrics = trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=self.args.kt_epochs,
            learning_rate=self.args.learning_rate,
            initial_anomaly_weight=self.args.anomaly_weight,
            use_cl=self.args.use_cl,
            start_epoch=start_epoch  # 传递起始轮次
        )
        # 训练器
        # trainer = KnowledgeTracingTrainer(
        #     model=model,
        #     device=self.device,
        #     save_dir=self.save_dir,
        #     patience=self.args.patience
        # )

        # 训练
        # anomaly_metrics = trainer.train(
        #     train_loader=train_data,
        #     val_loader=val_data,
        #     epochs=self.args.kt_epochs,
        #     learning_rate=self.args.learning_rate,
        #     use_cl=self.args.use_cl
        # )

        print(f"\nAnomaly-aware training completed!")
        print(f"Best AUC: {anomaly_metrics['auc']:.4f}")

        return os.path.join(self.save_dir, 'best_model.pt')


class ModelEvaluator(PipelineStage):
    """模型评估阶段"""

    def __init__(self, args: Any, device: str, output_dir: str):
        super().__init__(args, device, output_dir)

    def execute(self, dataset_config: Dict, test_data: KTData,
                baseline_path: str, anomaly_path: str, detector_path: str) -> Dict:
        """
        评估模型性能

        Args:
            dataset_config: 数据集配置
            test_data: 测试数据
            baseline_path: 基线模型路径
            anomaly_path: 异常感知模型路径
            detector_path: 异常检测器路径

        Returns:
            Dict: 评估结果
        """
        print("\n" + "="*60)
        print("PHASE 4: Model Evaluation")
        print("="*60)

        # 加载基线模型
        baseline_model = DTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers
        )
        baseline_model.load_state_dict(torch.load(baseline_path, map_location=self.device))
        baseline_model.to(self.device)

        # 加载异常检测器
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.detector_d_model,
            n_heads=self.args.detector_n_heads,
            n_layers=self.args.detector_n_layers,
            dropout=self.args.detector_dropout,
            window_size=self.args.window_size
        )
        checkpoint = torch.load(detector_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            detector.load_state_dict(checkpoint['model_state_dict'])
        else:
            detector.load_state_dict(checkpoint)
        detector.to(self.device)

        # 加载异常感知模型
        anomaly_model = AnomalyAwareDTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window,
            anomaly_detector=detector,
            anomaly_weight=self.args.anomaly_weight
        )
        anomaly_model.load_state_dict(torch.load(anomaly_path, map_location=self.device))
        anomaly_model.to(self.device)

        # 评估
        evaluator = ComparisonEvaluator()
        results = evaluator.evaluate_models(test_data, baseline_model, anomaly_model, self.device)

        # 打印结果
        evaluator.print_comparison(results)

        # 保存结果
        results_path = os.path.join(self.output_dir, 'evaluation_results.json')
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)

        print(f"\nResults saved to: {results_path}")

        return results


class PipelineManager:
    """流程管理器，负责协调各个阶段的执行"""

    def __init__(self, args: Any):
        """
        初始化流程管理器

        Args:
            args: 命令行参数
        """
        self.args = args
        self.device = args.device
        self.output_dir = args.output_dir

        # 创建各个阶段
        self.baseline_trainer = BaselineModelTrainer(args, self.device, self.output_dir)
        self.detector_trainer = AnomalyDetectorTrainer(args, self.device, self.output_dir)
        self.anomaly_trainer = AnomalyAwareModelTrainer(args, self.device, self.output_dir)
        self.evaluator = ModelEvaluator(args, self.device, self.output_dir)

    def run_pipeline(self, train_data: KTData, val_data: KTData, test_data: KTData,
                    dataset_config: Dict) -> Dict:
        """
        运行完整流程

        Args:
            train_data: 训练数据
            val_data: 验证数据
            test_data: 测试数据
            dataset_config: 数据集配置

        Returns:
            Dict: 评估结果
        """
        # 1. 训练基线模型
        if not self.args.skip_baseline:
            baseline_path = self.baseline_trainer.execute(dataset_config, train_data, val_data)
        else:
            baseline_path = self.args.baseline_path
            print(f"Using existing baseline model: {baseline_path}")

        # 2. 训练异常检测器
        if not self.args.skip_detector:
            detector_path = self.detector_trainer.execute(dataset_config, train_data, val_data)
        else:
            detector_path = self.args.detector_path
            print(f"Using existing detector: {detector_path}")

        # 3. 训练异常感知模型
        if not self.args.skip_anomaly_training:
            anomaly_path = self.anomaly_trainer.execute(dataset_config, train_data, val_data, detector_path)
        else:
            # 如果跳过训练，使用检查点作为最终模型
            anomaly_path = self.args.resume_from_checkpoint
            print(f"Using checkpoint as final model: {anomaly_path}")

        # 4. 评估结果
        results = self.evaluator.execute(dataset_config, test_data, baseline_path, anomaly_path, detector_path)

        print("\n" + "="*60)
        print("PIPELINE COMPLETED!")
        print("="*60)

        # 检查是否达到目标
        improvement = results['improvements']['auc']
        if improvement >= 1.0:
            print(f"✓ SUCCESS: Target achieved! AUC improved by {improvement:.2f}%")
        else:
            print(f"✗ Target not met. AUC improved by {improvement:.2f}% (need ≥1%)")
            print("\nSuggestions:")
            print("- Try adjusting anomaly_weight (current: {})".format(self.args.anomaly_weight))
            print("- Increase detector training epochs")
            print("- Experiment with different anomaly_ratio values")

        return results