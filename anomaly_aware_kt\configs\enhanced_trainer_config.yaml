# 增强异常检测器训练配置

# 数据参数
dataset:
  name: assist17           # 数据集名称 (assist09, assist17, algebra05, statics)
  data_dir: data           # 数据目录
  batch_size: 16           # 训练批次大小
  test_batch_size: 32      # 测试批次大小
  with_pid: true           # 是否使用问题ID

# 模型参数
model:
  detector:
    d_model: 128           # 模型维度
    n_heads: 8             # 注意力头数
    n_layers: 2            # 层数
    dropout: 0.1           # Dropout比例
    window_size: 15        # 窗口大小

# 训练参数
training:
  strategy: enhanced       # 训练策略 (basic, enhanced, aggressive)
  total_epochs: 50         # 总训练轮数
  learning_rate: 5e-4      # 学习率
  anomaly_ratio: 0.3       # 训练初始异常比例
  target_anomaly_ratio: 0.1  # 训练后期目标异常比例
  val_anomaly_ratio: 0.07   # 验证异常比例
  optimize_for: recall     # 优化目标 (recall, precision, f1_score, auc_roc)
  patience: 15             # 早停耐心值

# 继续训练参数
continue_training:
  enabled: true           # 是否从检查点继续训练
  checkpoint_dir: E:\work\DTransformer\output\assist17_20250527_133334\detector     # 检查点目录路径

# 其他参数
device: cuda               # 训练设备 (cuda, cpu)
save_dir: E:\work\DTransformer\output\assist17_20250527_133334\detector             # 保存目录，如果为null则自动生成