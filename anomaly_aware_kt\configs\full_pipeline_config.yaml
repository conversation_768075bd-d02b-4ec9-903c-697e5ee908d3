# full_pipeline.py 完整配置文件
# 支持所有命令行参数的配置

# ==================== 基本参数 ====================
# 数据集选择 (必需参数，通常通过命令行指定)
# dataset: "assist17"  # assist09, assist17, algebra05, statics

# 基本路径配置
data_dir: "data"
output_dir: null  # null表示自动生成时间戳目录
device: "auto"  # auto, cuda, cpu
with_pid: true  # 是否使用问题ID特征

# ==================== 数据参数 ====================
batch_size: 32
test_batch_size: 64

# ==================== 基线模型参数 (DTransformer) ====================
# 模型架构
d_model: 128
n_heads: 8
n_know: 32  # 推荐使用32而不是默认的16
n_layers: 3
dropout: 0.2

# 对比学习参数
lambda_cl: 0.1
proj: true  # 启用投影层
hard_neg: false  # 困难负样本
window: 1  # 滑动窗口

# ==================== 异常检测器参数 ====================
# 检测器架构
detector_d_model: 128
detector_n_heads: 8
detector_n_layers: 2
detector_dropout: 0.1
window_size: 10

# 异常检测配置
anomaly_ratio: 0.15  # 推荐使用0.15而不是0.1
optimize_for: "f1_score"  # f1_score, auc_roc, recall, precision, balanced_accuracy, mcc

# ==================== 训练参数 ====================
# 训练轮数
kt_epochs: 100
detector_epochs: 50  # 推荐增加到50轮

# 学习率
learning_rate: 1e-3
detector_lr: 1e-3

# 早停配置
patience: 15  # 推荐增加到15
detector_patience: 15

# 课程学习
use_cl: true  # 启用课程学习

# ==================== 异常感知参数 ====================
# 异常感知权重
anomaly_weight: 0.1  # 推荐从较小值开始

# 增强异常感知训练
use_enhanced_anomaly_aware: true
use_aa_curriculum: true  # 异常感知中的课程学习
use_aa_game_theory: true  # 异常感知中的博弈论

# ==================== 训练策略参数 ====================
# 检测器训练策略
training_strategy: "enhanced"  # basic, enhanced, aggressive
use_curriculum: true  # Enhanced策略的课程学习

# 向后兼容 (已弃用)
use_aggressive_strategy: false

# ==================== 控制参数 ====================
# 阶段跳过控制
skip_baseline: false
skip_detector: false
skip_anomaly_training: false

# 预训练模型路径 (如果跳过对应阶段)
baseline_path: null  # "output/baseline/best_model.pt"
detector_path: null  # "output/detector/best_model.pt"

# ==================== 检查点恢复参数 ====================
# 恢复训练配置
resume_from_checkpoint: null  # "output/epoch_9.pt"
start_epoch: 1

# ==================== 信息显示参数 ====================
# 信息显示控制
show_pipeline_info: false
show_strategies: false

# ==================== 预设配置模板 ====================
# 以下是不同场景的推荐配置，可以通过注释/取消注释来切换

# 快速测试配置
quick_test:
  kt_epochs: 20
  detector_epochs: 15
  batch_size: 64
  patience: 5
  detector_patience: 5

# 高性能配置
high_performance:
  kt_epochs: 150
  detector_epochs: 80
  batch_size: 16
  n_know: 64
  d_model: 256
  detector_d_model: 256
  patience: 25
  detector_patience: 20

# 资源受限配置
resource_limited:
  kt_epochs: 50
  detector_epochs: 25
  batch_size: 64
  n_know: 16
  d_model: 64
  detector_d_model: 64
  patience: 8
  detector_patience: 8

# ==================== 数据集特定配置 ====================
dataset_configs:
  assist09:
    recommended:
      batch_size: 16
      n_know: 16
      training_strategy: "enhanced"
      anomaly_ratio: 0.12
      
  assist17:
    recommended:
      batch_size: 32
      n_know: 32
      training_strategy: "enhanced"
      anomaly_ratio: 0.15
      
  algebra05:
    recommended:
      batch_size: 64
      n_know: 64
      training_strategy: "basic"
      anomaly_ratio: 0.08
      
  statics:
    recommended:
      batch_size: 16
      n_know: 32
      training_strategy: "aggressive"
      anomaly_ratio: 0.20

# ==================== 使用说明 ====================
# 使用方法：
# 1. 基本使用：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml
#
# 2. 覆盖配置：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml --batch_size 16
#
# 3. 跳过阶段：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml \
#        --skip_baseline --baseline_path output/baseline/best_model.pt
#
# 4. 恢复训练：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml \
#        --resume_from_checkpoint output/epoch_9.pt --start_epoch 10
