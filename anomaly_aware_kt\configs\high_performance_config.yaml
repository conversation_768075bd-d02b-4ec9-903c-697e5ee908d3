# 高性能配置 - 追求最佳性能
# 适用场景：正式实验、论文结果、生产环境

# 基本配置
data_dir: "data"
device: "cuda"  # 强制使用GPU
with_pid: true

# 数据参数 - 较小批次获得更好梯度估计
batch_size: 16
test_batch_size: 32

# 基线模型参数 - 大模型配置
d_model: 256
n_heads: 16
n_know: 64
n_layers: 6
dropout: 0.1  # 较低dropout
lambda_cl: 0.15
proj: true
hard_neg: true  # 启用困难负样本
window: 2

# 异常检测器参数 - 高性能配置
detector_d_model: 256
detector_n_heads: 16
detector_n_layers: 4
detector_dropout: 0.05
window_size: 15
anomaly_ratio: 0.15
optimize_for: "f1_score"

# 训练参数 - 充分训练
kt_epochs: 150
detector_epochs: 80
learning_rate: 5e-4  # 较低学习率确保稳定
detector_lr: 5e-4
patience: 25
detector_patience: 20
use_cl: true

# 异常感知参数 - 全功能启用
anomaly_weight: 0.08  # 较小初始权重
use_enhanced_anomaly_aware: true
use_aa_curriculum: true
use_aa_game_theory: true

# 训练策略 - 使用增强策略
training_strategy: "enhanced"
use_curriculum: true

# 控制参数
skip_baseline: false
skip_detector: false
skip_anomaly_training: false
