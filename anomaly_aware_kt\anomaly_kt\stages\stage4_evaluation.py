"""
第4阶段：模型评估与对比

对比基线模型和异常感知模型的性能，评估异常感知能力的实际效果。
包括在干净数据和异常干扰数据上的全面评估。
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Any, Optional
from DTransformer.data import KTData
from DTransformer.model import DTransformer
from ..detector import CausalAnomalyDetector
from ..model import AnomalyAwareDTransformer
from ..evaluator import ComparisonEvaluator, plot_training_curves


class EvaluationStage:
    """第4阶段：模型评估与对比"""
    
    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化评估阶段
        
        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir
        self.save_dir = os.path.join(output_dir, 'evaluation')
        os.makedirs(self.save_dir, exist_ok=True)
        
    def execute(self, dataset_config: Dict, test_data: KTData,
               baseline_path: str, anomaly_aware_path: str, 
               detector_path: Optional[str] = None) -> Dict:
        """
        执行模型评估与对比
        
        Args:
            dataset_config: 数据集配置
            test_data: 测试数据
            baseline_path: 基线模型路径
            anomaly_aware_path: 异常感知模型路径
            detector_path: 异常检测器路径（可选）
            
        Returns:
            Dict: 评估结果
        """
        print("\n" + "="*60)
        print("STAGE 4: Model Evaluation and Comparison")
        print("="*60)
        print("🎯 目标: 全面评估异常感知能力的效果")
        print("📊 对比: 基线模型 vs 异常感知模型")
        
        # 加载模型
        baseline_model = self._load_baseline_model(dataset_config, baseline_path)
        anomaly_aware_model = self._load_anomaly_aware_model(dataset_config, anomaly_aware_path, detector_path)
        
        print(f"✓ 基线模型已加载: {baseline_path}")
        print(f"✓ 异常感知模型已加载: {anomaly_aware_path}")
        
        # 创建评估器
        evaluator = self._create_evaluator()
        
        # 执行评估
        results = self._perform_evaluation(evaluator, test_data, baseline_model, anomaly_aware_model)
        
        # 分析结果
        analysis = self._analyze_results(results)
        
        # 保存结果
        self._save_evaluation_results(results, analysis)
        
        # 显示总结
        self._display_summary(results, analysis)
        
        return {
            'evaluation_results': results,
            'analysis': analysis,
            'success': analysis['target_achieved']
        }
        
    def _load_baseline_model(self, dataset_config: Dict, baseline_path: str) -> nn.Module:
        """加载基线模型"""
        model = DTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window
        )
        
        # 加载检查点
        checkpoint = torch.load(baseline_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        model.to(self.device)
        model.eval()
        
        return model
        
    def _load_anomaly_aware_model(self, dataset_config: Dict, anomaly_aware_path: str,
                                 detector_path: Optional[str] = None) -> nn.Module:
        """加载异常感知模型"""
        # 如果需要检测器，先加载检测器
        detector = None
        if detector_path:
            detector = CausalAnomalyDetector(
                n_questions=dataset_config['n_questions'],
                n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
                d_model=getattr(self.args, 'detector_d_model', self.args.d_model),
                n_heads=getattr(self.args, 'detector_n_heads', self.args.n_heads),
                n_layers=getattr(self.args, 'detector_n_layers', 2),
                dropout=getattr(self.args, 'detector_dropout', self.args.dropout),
                window_size=getattr(self.args, 'window_size', 10)
            )
            
            detector_checkpoint = torch.load(detector_path, map_location=self.device)
            if 'model_state_dict' in detector_checkpoint:
                detector.load_state_dict(detector_checkpoint['model_state_dict'])
            else:
                detector.load_state_dict(detector_checkpoint)
            detector.to(self.device)
            detector.eval()
        
        # 创建异常感知模型
        model = AnomalyAwareDTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window,
            anomaly_detector=detector,
            anomaly_weight=getattr(self.args, 'anomaly_weight', 0.5)
        )
        
        # 加载检查点
        checkpoint = torch.load(anomaly_aware_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
            
        model.to(self.device)
        model.eval()
        
        return model
        
    def _create_evaluator(self) -> ComparisonEvaluator:
        """创建比较评估器"""
        return ComparisonEvaluator(device=self.device)
        
    def _perform_evaluation(self, evaluator: ComparisonEvaluator, test_data: KTData,
                           baseline_model: nn.Module, anomaly_aware_model: nn.Module) -> Dict:
        """执行评估"""
        print(f"\n🔍 开始模型评估...")
        print(f"📊 测试数据批次: {len(test_data)}")
        
        # 在测试数据上评估两个模型
        results = evaluator.evaluate_models(
            test_loader=test_data,
            baseline_model=baseline_model,
            anomaly_aware_model=anomaly_aware_model,
            device=self.device
        )
        
        return results
        
    def _analyze_results(self, results: Dict) -> Dict:
        """分析评估结果"""
        baseline_metrics = results['baseline']
        anomaly_aware_metrics = results['anomaly_aware']
        
        # 计算改进幅度
        improvements = {}
        for metric in ['acc', 'auc', 'mae', 'rmse']:
            if metric in baseline_metrics and metric in anomaly_aware_metrics:
                baseline_val = baseline_metrics[metric]
                anomaly_val = anomaly_aware_metrics[metric]
                
                if metric in ['mae', 'rmse']:  # 越小越好
                    improvement = ((baseline_val - anomaly_val) / baseline_val) * 100
                else:  # 越大越好
                    improvement = ((anomaly_val - baseline_val) / baseline_val) * 100
                    
                improvements[metric] = improvement
        
        # 检查是否达到目标
        auc_improvement = improvements.get('auc', 0)
        target_achieved = auc_improvement >= 1.0  # 目标：AUC提升≥1%
        
        analysis = {
            'improvements': improvements,
            'target_achieved': target_achieved,
            'auc_improvement': auc_improvement,
            'best_metric': max(improvements.items(), key=lambda x: x[1]) if improvements else None,
            'summary': self._generate_summary_text(improvements, target_achieved)
        }
        
        return analysis
        
    def _generate_summary_text(self, improvements: Dict, target_achieved: bool) -> str:
        """生成总结文本"""
        if target_achieved:
            return f"✅ 成功达成目标！AUC提升 {improvements.get('auc', 0):.2f}%"
        else:
            return f"⚠️ 未达成目标。AUC提升 {improvements.get('auc', 0):.2f}% (目标: ≥1%)"
            
    def _save_evaluation_results(self, results: Dict, analysis: Dict):
        """保存评估结果"""
        import json
        
        # 保存详细结果
        results_path = os.path.join(self.save_dir, 'evaluation_results.json')
        with open(results_path, 'w') as f:
            json.dump({
                'results': results,
                'analysis': analysis
            }, f, indent=2)
            
        # 生成可视化图表
        try:
            plot_path = os.path.join(self.save_dir, 'comparison_plot.png')
            plot_training_curves(results, save_path=plot_path)
            print(f"📊 对比图表已保存: {plot_path}")
        except Exception as e:
            print(f"⚠️ 图表生成失败: {e}")
            
    def _display_summary(self, results: Dict, analysis: Dict):
        """显示评估总结"""
        print(f"\n" + "="*60)
        print("📊 EVALUATION SUMMARY")
        print("="*60)
        
        print(f"\n📈 性能对比:")
        baseline_metrics = results['baseline']
        anomaly_aware_metrics = results['anomaly_aware']
        
        for metric in ['acc', 'auc', 'mae', 'rmse']:
            if metric in baseline_metrics and metric in anomaly_aware_metrics:
                baseline_val = baseline_metrics[metric]
                anomaly_val = anomaly_aware_metrics[metric]
                improvement = analysis['improvements'].get(metric, 0)
                
                print(f"  {metric.upper():4s}: {baseline_val:.4f} → {anomaly_val:.4f} "
                      f"({improvement:+.2f}%)")
        
        print(f"\n🎯 目标达成情况:")
        print(f"  {analysis['summary']}")
        
        if not analysis['target_achieved']:
            print(f"\n💡 改进建议:")
            print(f"  1. 调整异常权重 (当前: {getattr(self.args, 'anomaly_weight', 0.5)})")
            print(f"  2. 增加训练轮数")
            print(f"  3. 尝试不同的训练策略")
            print(f"  4. 调整检测器参数")
            
        print(f"\n📁 详细结果已保存至: {self.save_dir}")
        
    def get_stage_info(self) -> Dict:
        """获取阶段信息"""
        return {
            'stage_number': 4,
            'stage_name': 'evaluation',
            'description': '模型评估与对比',
            'purpose': '评估异常感知能力的实际效果',
            'output': '评估报告和对比结果',
            'dependencies': ['baseline_model', 'anomaly_aware_model'],
            'next_stage': None,
            'evaluation_metrics': ['accuracy', 'auc', 'mae', 'rmse']
        }
