"""
异常检测器训练策略模块

提供三种不同的训练策略，每种策略都有独立的实现：
1. BasicStrategy: 基础策略 - 标准训练流程
2. EnhancedStrategy: 增强策略 - 课程学习 + 动态调整
3. AggressiveStrategy: 激进策略 - 极端不平衡处理
"""

from .base_strategy import BaseDetectorStrategy
from .basic_strategy import BasicDetectorStrategy
from .enhanced_strategy import EnhancedDetectorStrategy
from .aggressive_strategy import AggressiveDetectorStrategy

# 策略工厂
class DetectorStrategyFactory:
    """异常检测器训练策略工厂"""
    
    _strategies = {
        'basic': BasicDetectorStrategy,
        'enhanced': EnhancedDetectorStrategy,
        'improved': EnhancedDetectorStrategy,  # 向后兼容
        'aggressive': AggressiveDetectorStrategy
    }
    
    @classmethod
    def create_strategy(cls, strategy_name: str, model, device: str, save_dir: str, patience: int):
        """创建训练策略实例"""
        strategy_name = strategy_name.lower()
        if strategy_name not in cls._strategies:
            raise ValueError(f"Unknown strategy: {strategy_name}. Available: {list(cls._strategies.keys())}")
        
        return cls._strategies[strategy_name](model, device, save_dir, patience)
    
    @classmethod
    def get_available_strategies(cls) -> list:
        """获取可用策略列表"""
        return list(cls._strategies.keys())
    
    @classmethod
    def get_strategy_info(cls, strategy_name: str) -> dict:
        """获取策略信息"""
        strategy_descriptions = {
            'basic': {
                'name': 'Basic Strategy',
                'description': '基础策略 - 标准训练流程',
                'features': ['标准BCE损失', '固定异常比例', '简单数据增强'],
                'best_for': '快速原型和基准测试'
            },
            'enhanced': {
                'name': 'Enhanced Strategy', 
                'description': '增强策略 - 课程学习 + 动态调整',
                'features': ['课程学习', '动态异常比例', 'Focal Loss', '对比学习'],
                'best_for': '平衡性能和训练稳定性'
            },
            'aggressive': {
                'name': 'Aggressive Strategy',
                'description': '激进策略 - 极端不平衡处理',
                'features': ['极端类别权重', '强制批次平衡', '多损失函数', '动态学习率'],
                'best_for': '处理严重的类别不平衡问题'
            }
        }
        
        return strategy_descriptions.get(strategy_name.lower(), {})


__all__ = [
    "BaseDetectorStrategy",
    "BasicDetectorStrategy", 
    "EnhancedDetectorStrategy",
    "AggressiveDetectorStrategy",
    "DetectorStrategyFactory",
]
