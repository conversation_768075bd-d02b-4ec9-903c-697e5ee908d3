# 资源受限配置 - 适用于计算资源有限的环境
# 适用场景：个人电脑、CPU训练、内存受限

# 基本配置
data_dir: "data"
device: "auto"
with_pid: false  # 关闭PID特征减少内存

# 数据参数 - 大批次减少迭代次数
batch_size: 128
test_batch_size: 256

# 基线模型参数 - 轻量级模型
d_model: 64
n_heads: 4
n_know: 16
n_layers: 2
dropout: 0.3
lambda_cl: 0.05
proj: false  # 关闭投影层
hard_neg: false
window: 1

# 异常检测器参数 - 最小配置
detector_d_model: 64
detector_n_heads: 4
detector_n_layers: 1
detector_dropout: 0.2
window_size: 5
anomaly_ratio: 0.08
optimize_for: "auc_roc"  # AUC计算开销较小

# 训练参数 - 快速收敛
kt_epochs: 50
detector_epochs: 25
learning_rate: 2e-3
detector_lr: 2e-3
patience: 8
detector_patience: 8
use_cl: false  # 关闭课程学习

# 异常感知参数 - 最简配置
anomaly_weight: 0.2
use_enhanced_anomaly_aware: false  # 关闭增强训练
use_aa_curriculum: false
use_aa_game_theory: false

# 训练策略 - 基础策略
training_strategy: "basic"
use_curriculum: false

# 控制参数
skip_baseline: false
skip_detector: false
skip_anomaly_training: false
