"""
增强的异常感知知识跟踪训练器

整合课程学习、博弈论和动态调整策略
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Optional, Tuple, List
from collections import defaultdict
from tqdm import tqdm

from .trainer import BaseTrainer
from .evaluator import AnomalyEvaluator
from DTransformer.eval import Evaluator as KTEvaluator


class AnomalyAwareCurriculum:
    """异常感知模型的课程学习调度器"""

    def __init__(self, total_epochs: int):
        self.total_epochs = total_epochs
        self.stages = [
            (0.3, {
                'name': 'foundation',
                'anomaly_weight': 0.1,
                'anomaly_threshold': 0.7,
                'learning_focus': 'normal_patterns',
                'description': '基础阶段：建立正常行为模式的稳固理解'
            }),
            (0.6, {
                'name': 'integration',
                'anomaly_weight': 0.3,
                'anomaly_threshold': 0.5,
                'learning_focus': 'soft_anomalies',
                'description': '整合阶段：逐步引入异常信息'
            }),
            (1.0, {
                'name': 'refinement',
                'anomaly_weight': 0.5,
                'anomaly_threshold': 0.3,
                'learning_focus': 'full_spectrum',
                'description': '精化阶段：全谱异常感知与平衡'
            })
        ]

    def get_stage_params(self, epoch: int) -> Dict:
        """获取当前阶段参数"""
        progress = epoch / self.total_epochs

        for threshold, params in self.stages:
            if progress <= threshold:
                return params

        return self.stages[-1][1]


class DynamicAnomalyWeight:
    """动态异常权重调整器"""

    def __init__(self, base_weight: float = 0.5, window_size: int = 5):
        self.base_weight = base_weight
        self.window_size = window_size
        self.history = defaultdict(list)

    def update(self, metrics: Dict):
        """更新性能历史"""
        for key, value in metrics.items():
            self.history[key].append(value)

    def get_weight(self, epoch: int, stage_params: Dict) -> float:
        """计算当前应使用的异常权重"""
        stage_weight = stage_params.get('anomaly_weight', self.base_weight)

        # 如果历史数据不足，使用阶段默认权重
        if len(self.history['auc']) < self.window_size:
            return stage_weight

        # 基于最近性能调整
        recent_auc = np.mean(self.history['auc'][-self.window_size:])
        recent_acc = np.mean(self.history['acc'][-self.window_size:])

        # 性能良好时可以增加异常权重
        if recent_auc > 0.85 and recent_acc > 0.75:
            adjustment = 1.2
        # 性能下降时减少异常权重
        elif recent_auc < 0.80 or recent_acc < 0.70:
            adjustment = 0.8
        else:
            adjustment = 1.0

        return min(0.8, stage_weight * adjustment)


class EnhancedAnomalyAwareTrainer(BaseTrainer):
    """增强的异常感知知识跟踪训练器"""

    def __init__(
        self,
        model: nn.Module,
        detector: nn.Module,
        device: str = 'cpu',
        save_dir: str = 'output/enhanced_anomaly_aware',
        patience: int = 15,
        use_curriculum: bool = True,
        use_game_theory: bool = True
    ):
        super().__init__(model, device, save_dir, patience)
        self.detector = detector.to(device)
        self.detector.eval()  # 检测器保持评估模式

        self.use_curriculum = use_curriculum
        self.use_game_theory = use_game_theory

        # 评估器
        self.kt_evaluator = KTEvaluator()
        self.anomaly_evaluator = AnomalyEvaluator()

        # 动态组件
        self.weight_adjuster = DynamicAnomalyWeight()

        # 记录额外指标
        self.anomaly_impact_history = []

    def train(
        self,
        train_loader,
        val_loader,
        epochs: int = 100,
        learning_rate: float = 1e-3,
        initial_anomaly_weight: float = 0.5,
        use_cl: bool = True,
        start_epoch: int = 1,
        **kwargs
    ) -> Dict:
        """增强的训练流程"""

        # 初始化课程
        if self.use_curriculum:
            curriculum = AnomalyAwareCurriculum(epochs)
            print("📚 使用课程学习策略")
        else:
            curriculum = None

        # 初始化优化器
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-5
        )

        # 学习率调度器 - 使用余弦退火
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=epochs, eta_min=1e-6
        )

        # 保存配置
        config = {
            'epochs': epochs,
            'learning_rate': learning_rate,
            'initial_anomaly_weight': initial_anomaly_weight,
            'use_cl': use_cl,
            'use_curriculum': self.use_curriculum,
            'use_game_theory': self.use_game_theory,
            **kwargs
        }

        with open(os.path.join(self.save_dir, 'config.json'), 'w') as f:
            import json
            json.dump(config, f, indent=2)

        print("\n🚀 开始增强的异常感知训练")
        if start_epoch > 1:
            print(f"📊 从第{start_epoch}轮恢复训练, 总轮数: {epochs}, 初始异常权重: {initial_anomaly_weight}")
        else:
            print(f"📊 总轮数: {epochs}, 初始异常权重: {initial_anomaly_weight}")

        best_score = 0
        no_improve = 0

        for epoch in range(start_epoch - 1, epochs):
            # 获取当前阶段参数
            if curriculum:
                stage_params = curriculum.get_stage_params(epoch)
                current_weight = self.weight_adjuster.get_weight(epoch, stage_params)

                # 更新模型的异常权重
                self.model.anomaly_weight = current_weight

                print(f"\n{'='*20} Epoch {epoch+1}/{epochs} - {stage_params['name']} {'='*20}")
                print(f"📋 {stage_params['description']}")
                print(f"⚖️  异常权重: {current_weight:.3f}")
            else:
                current_weight = initial_anomaly_weight
                print(f"\n{'='*20} Epoch {epoch+1}/{epochs} {'='*20}")

            # 训练
            train_metrics = self._train_epoch_enhanced(
                train_loader, optimizer, epoch, epochs, use_cl
            )

            # 验证
            val_metrics = self._validate_epoch_enhanced(val_loader)

            # 更新历史
            self.weight_adjuster.update(val_metrics)
            for k, v in train_metrics.items():
                self.history[f'train_{k}'].append(v)
            for k, v in val_metrics.items():
                self.history[f'val_{k}'].append(v)

            # 计算异常影响
            anomaly_impact = self._calculate_anomaly_impact(val_metrics)
            self.anomaly_impact_history.append(anomaly_impact)

            # 打印结果
            self._print_epoch_results(epoch + 1, train_metrics, val_metrics, anomaly_impact)

            # 调整学习率
            scheduler.step()

            # 检查最佳模型
            current_score = val_metrics['auc']
            if current_score > best_score:
                best_score = current_score
                self.best_metrics = val_metrics.copy()
                self.best_epoch = epoch + 1
                self.save_checkpoint(epoch, optimizer, val_metrics, is_best=True)
                no_improve = 0
                print(f"  ✅ 新的最佳 AUC: {current_score:.4f}")
            else:
                no_improve += 1

            # 早停检查
            if no_improve >= self.patience:
                print(f"\n⏹️  早停: {no_improve} 轮无改善")
                break

        print(f"\n🎉 训练完成!")
        print(f"  🏆 最佳轮次: {self.best_epoch}")
        print(f"  📈 最佳 AUC: {best_score:.4f}")

        return self.best_metrics

    def _train_epoch_enhanced(
        self,
        train_loader,
        optimizer,
        epoch: int,
        total_epochs: int,
        use_cl: bool
    ) -> Dict:
        """增强的训练epoch"""
        self.model.train()

        total_loss = 0
        total_kt_loss = 0
        total_anomaly_loss = 0
        n_batches = 0

        progress_bar = tqdm(train_loader, desc="Training")

        for batch in progress_bar:
            q, s, pid = self._get_batch_data(batch)

            # 博弈论损失计算
            if self.use_game_theory:
                loss, kt_loss, anomaly_loss = self._compute_game_theoretic_loss(
                    q, s, pid, epoch, total_epochs
                )
                total_kt_loss += kt_loss
                total_anomaly_loss += anomaly_loss
            else:
                # 标准损失
                if use_cl and hasattr(self.model, 'get_cl_loss'):
                    loss, _, _ = self.model.get_cl_loss(q, s, pid)
                else:
                    loss = self.model.get_loss(q, s, pid, epoch, total_epochs)

            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            optimizer.step()

            total_loss += loss.item()
            n_batches += 1

            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg_Loss': f'{total_loss/n_batches:.4f}'
            })

        metrics = {
            'loss': total_loss / n_batches,
            'kt_loss': total_kt_loss / n_batches if self.use_game_theory else 0,
            'anomaly_loss': total_anomaly_loss / n_batches if self.use_game_theory else 0
        }

        return metrics

    def _validate_epoch_enhanced(self, val_loader) -> Dict:
        """增强的验证epoch"""
        self.model.eval()
        # 重新创建评估器来重置状态
        self.kt_evaluator = KTEvaluator()

        normal_predictions = []
        anomaly_predictions = []
        normal_labels = []
        anomaly_labels = []
        all_anomaly_scores = []

        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                q, s, pid = self._get_batch_data(batch)

                # 预测
                if hasattr(self.model, 'predict_with_anomaly'):
                    y, _, _, _, _, anomaly_scores = self.model.predict_with_anomaly(q, s, pid)
                else:
                    y, *_ = self.model.predict(q, s, pid)
                    anomaly_scores = torch.zeros_like(s).float()

                # 评估KT性能
                self.kt_evaluator.evaluate(s, torch.sigmoid(y))

                # 收集异常分数用于统计
                mask = s >= 0
                all_anomaly_scores.extend(anomaly_scores[mask].cpu().numpy())

                # 收集正常/异常预测和标签
                anomaly_mask = (anomaly_scores > 0.5) & mask
                normal_mask = (anomaly_scores <= 0.5) & mask

                if normal_mask.any():
                    normal_predictions.extend(torch.sigmoid(y[normal_mask]).cpu().numpy())
                    normal_labels.extend(s[normal_mask].cpu().numpy())
                if anomaly_mask.any():
                    anomaly_predictions.extend(torch.sigmoid(y[anomaly_mask]).cpu().numpy())
                    anomaly_labels.extend(s[anomaly_mask].cpu().numpy())

        # 保存异常分数用于调试
        self._last_anomaly_scores = torch.tensor(all_anomaly_scores)

        # 基础指标
        metrics = self.kt_evaluator.report()

        # 分析正常/异常性能差异 - 修复MAE计算
        if normal_predictions and normal_labels:
            metrics['normal_mae'] = np.mean(np.abs(np.array(normal_predictions) - np.array(normal_labels)))
        if anomaly_predictions and anomaly_labels:
            metrics['anomaly_mae'] = np.mean(np.abs(np.array(anomaly_predictions) - np.array(anomaly_labels)))

        if 'normal_mae' in metrics and 'anomaly_mae' in metrics:
            metrics['performance_gap'] = metrics['normal_mae'] - metrics['anomaly_mae']

        # 添加调试信息
        print(f"  🔍 异常检测统计:")
        if hasattr(self, '_last_anomaly_scores'):
            print(f"    异常分数均值: {self._last_anomaly_scores.mean():.3f}")
            print(f"    异常分数标准差: {self._last_anomaly_scores.std():.3f}")
            print(f"    >0.5的样本比例: {(self._last_anomaly_scores > 0.5).float().mean():.1%}")
        print(f"    正常样本数: {len(normal_predictions)}")
        print(f"    异常样本数: {len(anomaly_predictions)}")

        return metrics

    def _compute_game_theoretic_loss(
        self,
        q: torch.Tensor,
        s: torch.Tensor,
        pid: Optional[torch.Tensor],
        epoch: int,
        total_epochs: int
    ) -> Tuple[torch.Tensor, float, float]:
        """基于博弈论的损失计算"""

        # 获取预测和异常分数
        if hasattr(self.model, 'predict_with_anomaly'):
            y, _, _, reg_loss, _, anomaly_scores = self.model.predict_with_anomaly(q, s, pid)
        else:
            y, *_ = self.model.predict(q, s, pid)
            anomaly_scores = torch.zeros_like(s).float()
            reg_loss = 0

        mask = s >= 0
        masked_labels = s[mask].float()
        masked_logits = y[mask]
        masked_anomaly = anomaly_scores[mask]

        # 分离正常和异常样本
        normal_mask = masked_anomaly < 0.3
        anomaly_mask = masked_anomaly > 0.7
        uncertain_mask = ~(normal_mask | anomaly_mask)

        # 1. 正常样本：标准KT损失
        if normal_mask.any():
            normal_loss = F.binary_cross_entropy_with_logits(
                masked_logits[normal_mask],
                masked_labels[normal_mask]
            )
        else:
            normal_loss = torch.tensor(0.0).to(self.device)

        # 2. 异常样本：鼓励不确定性
        if anomaly_mask.any():
            # 推向0.5（最大不确定性）
            uncertainty_target = torch.full_like(masked_logits[anomaly_mask], 0.5)
            anomaly_loss = F.mse_loss(
                torch.sigmoid(masked_logits[anomaly_mask]),
                uncertainty_target
            )
        else:
            anomaly_loss = torch.tensor(0.0).to(self.device)

        # 3. 不确定样本：软标签
        if uncertain_mask.any():
            # 使用加权损失
            weight = 1.0 - masked_anomaly[uncertain_mask]
            uncertain_loss = F.binary_cross_entropy_with_logits(
                masked_logits[uncertain_mask],
                masked_labels[uncertain_mask],
                reduction='none'
            )
            uncertain_loss = (uncertain_loss * weight).mean()
        else:
            uncertain_loss = torch.tensor(0.0).to(self.device)

        # 动态权重
        progress = epoch / total_epochs
        if progress < 0.3:
            # 早期：专注正常样本
            weights = {'normal': 0.8, 'anomaly': 0.1, 'uncertain': 0.1}
        elif progress < 0.7:
            # 中期：平衡
            weights = {'normal': 0.5, 'anomaly': 0.3, 'uncertain': 0.2}
        else:
            # 后期：精细化
            weights = {'normal': 0.4, 'anomaly': 0.4, 'uncertain': 0.2}

        total_loss = (
            weights['normal'] * normal_loss +
            weights['anomaly'] * anomaly_loss +
            weights['uncertain'] * uncertain_loss +
            reg_loss
        )

        return total_loss, normal_loss.item(), anomaly_loss.item()

    def _calculate_anomaly_impact(self, metrics: Dict) -> Dict:
        """计算异常对性能的影响"""
        impact = {}

        if 'normal_mae' in metrics and 'anomaly_mae' in metrics:
            # 性能差距
            impact['performance_gap'] = metrics['normal_mae'] - metrics['anomaly_mae']

            # 相对影响：基于性能差距计算
            if metrics['mae'] > 0:
                impact['relative_impact'] = (
                    abs(impact['performance_gap']) / metrics['mae']
                )
            else:
                impact['relative_impact'] = 0

        return impact

    def _print_epoch_results(
        self,
        epoch: int,
        train_metrics: Dict,
        val_metrics: Dict,
        anomaly_impact: Dict
    ):
        """打印训练结果"""
        print(f"\n📊 Epoch {epoch} 结果:")

        # 训练指标
        print(f"  📈 训练 - Loss: {train_metrics['loss']:.4f}")
        if self.use_game_theory:
            print(f"    KT Loss: {train_metrics['kt_loss']:.4f}, "
                  f"Anomaly Loss: {train_metrics['anomaly_loss']:.4f}")

        # 验证指标
        print(f"  📊 验证 - ACC: {val_metrics['acc']:.3f}, "
              f"AUC: {val_metrics['auc']:.3f}, "
              f"MAE: {val_metrics['mae']:.3f}")

        # 异常影响
        if anomaly_impact:
            print(f"  🔍 异常影响:")
            if 'performance_gap' in anomaly_impact:
                print(f"    性能差距: {anomaly_impact['performance_gap']:.3f}")
            if 'relative_impact' in anomaly_impact:
                print(f"    相对影响: {anomaly_impact['relative_impact']:.1%}")

    def _get_batch_data(self, batch):
        """获取批次数据"""
        if len(batch.data) == 2:
            q, s = batch.get("q", "s")
            pid = None
        else:
            q, s, pid = batch.get("q", "s", "pid")

        q = q.to(self.device)
        s = s.to(self.device)
        if pid is not None:
            pid = pid.to(self.device)

        return q, s, pid

    def save_training_summary(self):
        """保存训练总结"""
        summary = {
            'best_epoch': self.best_epoch,
            'best_metrics': self.best_metrics,
            'anomaly_impact_history': self.anomaly_impact_history,
            'final_anomaly_weight': self.model.anomaly_weight,
            'training_history': dict(self.history)
        }

        summary_path = os.path.join(self.save_dir, 'training_summary.json')
        with open(summary_path, 'w') as f:
            import json
            json.dump(summary, f, indent=2)

        print(f"\n📝 训练总结已保存至: {summary_path}")
