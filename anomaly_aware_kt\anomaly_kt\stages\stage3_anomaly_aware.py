"""
第3阶段：异常感知知识追踪训练

将异常检测器集成到知识追踪模型中，训练具备异常感知能力的模型。
使用增强的训练策略，包括课程学习和博弈论方法。
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Any
from DTransformer.data import KTData
from ..detector import CausalAnomalyDetector
from ..model import AnomalyAwareDTransformer
from ..enhanced_anomaly_aware_trainer import EnhancedAnomalyAwareTrainer


class AnomalyAwareTrainingStage:
    """第3阶段：异常感知知识追踪训练"""
    
    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化异常感知训练阶段
        
        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir
        self.save_dir = os.path.join(output_dir, 'anomaly_aware')
        os.makedirs(self.save_dir, exist_ok=True)
        
    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData, 
               detector_path: str) -> str:
        """
        执行异常感知模型训练
        
        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据
            detector_path: 异常检测器模型路径
            
        Returns:
            str: 保存的异常感知模型路径
        """
        print("\n" + "="*60)
        print("STAGE 3: Training Anomaly-Aware Knowledge Tracing Model")
        print("="*60)
        print("🎯 目标: 训练具备异常感知能力的知识追踪模型")
        print("🧠 特点: 集成异常检测器，使用增强训练策略")
        
        # 加载异常检测器
        detector = self._load_detector(dataset_config, detector_path)
        print(f"✓ 异常检测器已加载: {detector_path}")
        
        # 创建异常感知模型
        model = self._create_anomaly_aware_model(dataset_config, detector)
        print(f"🧠 异常感知模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        # 显示训练配置
        self._display_training_config()
        
        # 处理检查点恢复
        start_epoch = self._handle_checkpoint_resume(model)
        
        # 创建增强训练器
        trainer = self._create_enhanced_trainer(model, detector)
        
        # 执行训练
        anomaly_metrics = self._train_anomaly_aware_model(trainer, train_data, val_data, start_epoch)
        
        # 保存结果
        model_path = self._save_results(anomaly_metrics)
        
        print(f"\n✅ Stage 3 完成!")
        print(f"📈 最佳 AUC: {anomaly_metrics['auc']:.4f}")
        print(f"💾 模型保存至: {model_path}")
        
        return model_path
        
    def _load_detector(self, dataset_config: Dict, detector_path: str) -> nn.Module:
        """加载训练好的异常检测器"""
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=getattr(self.args, 'detector_d_model', self.args.d_model),
            n_heads=getattr(self.args, 'detector_n_heads', self.args.n_heads),
            n_layers=getattr(self.args, 'detector_n_layers', 2),
            dropout=getattr(self.args, 'detector_dropout', self.args.dropout),
            window_size=getattr(self.args, 'window_size', 10)
        )
        
        # 加载检查点
        checkpoint = torch.load(detector_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            detector.load_state_dict(checkpoint['model_state_dict'])
        else:
            detector.load_state_dict(checkpoint)
            
        detector.to(self.device)
        detector.eval()  # 检测器保持评估模式
        
        return detector
        
    def _create_anomaly_aware_model(self, dataset_config: Dict, detector: nn.Module) -> nn.Module:
        """创建异常感知DTransformer模型"""
        model = AnomalyAwareDTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window,
            anomaly_detector=detector,
            anomaly_weight=getattr(self.args, 'anomaly_weight', 0.5)
        )
        return model.to(self.device)
        
    def _display_training_config(self):
        """显示训练配置信息"""
        print(f"📋 训练配置:")
        print(f"  📊 总轮数: {getattr(self.args, 'kt_epochs', 100)}")
        print(f"  📈 学习率: {self.args.learning_rate}")
        print(f"  ⚖️  异常权重: {getattr(self.args, 'anomaly_weight', 0.5)}")
        print(f"  🎯 使用课程学习: {getattr(self.args, 'use_cl', True)}")
        print(f"  🎮 使用博弈论: True")
        
    def _handle_checkpoint_resume(self, model: nn.Module) -> int:
        """处理检查点恢复"""
        start_epoch = 1
        
        if (hasattr(self.args, 'resume_from_checkpoint') and 
            self.args.resume_from_checkpoint):
            print(f"\n📂 加载检查点: {self.args.resume_from_checkpoint}")
            
            checkpoint = torch.load(self.args.resume_from_checkpoint, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print("✓ 模型状态已加载 (带metadata格式)")
            else:
                model.load_state_dict(checkpoint)
                print("✓ 模型状态已加载 (直接格式)")
                
            if hasattr(self.args, 'start_epoch') and self.args.start_epoch:
                start_epoch = self.args.start_epoch
                print(f"✓ 将从第{start_epoch}轮开始训练")
                
        return start_epoch
        
    def _create_enhanced_trainer(self, model: nn.Module, detector: nn.Module) -> EnhancedAnomalyAwareTrainer:
        """创建增强的异常感知训练器"""
        return EnhancedAnomalyAwareTrainer(
            model=model,
            detector=detector,
            device=self.device,
            save_dir=self.save_dir,
            patience=self.args.patience,
            use_curriculum=True,  # 启用课程学习
            use_game_theory=True  # 启用博弈论
        )
        
    def _train_anomaly_aware_model(self, trainer: EnhancedAnomalyAwareTrainer,
                                  train_data: KTData, val_data: KTData, start_epoch: int) -> Dict:
        """执行异常感知模型训练"""
        print(f"\n🚀 开始异常感知模型训练...")
        if start_epoch > 1:
            print(f"📂 从第{start_epoch}轮恢复训练")
            
        return trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=getattr(self.args, 'kt_epochs', 100),
            learning_rate=self.args.learning_rate,
            initial_anomaly_weight=getattr(self.args, 'anomaly_weight', 0.5),
            use_cl=getattr(self.args, 'use_cl', True),
            start_epoch=start_epoch
        )
        
    def _save_results(self, metrics: Dict) -> str:
        """保存训练结果"""
        # 保存训练指标
        import json
        metrics_path = os.path.join(self.save_dir, 'anomaly_aware_metrics.json')
        with open(metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)
            
        # 返回最佳模型路径
        return os.path.join(self.save_dir, 'best_model.pt')
        
    def get_stage_info(self) -> Dict:
        """获取阶段信息"""
        return {
            'stage_number': 3,
            'stage_name': 'anomaly_aware_training',
            'description': '异常感知知识追踪训练',
            'purpose': '训练具备异常感知能力的知识追踪模型',
            'output': '训练好的异常感知模型',
            'dependencies': ['detector_model'],
            'next_stage': 'evaluation',
            'features': ['课程学习', '博弈论', '动态权重调整', '检查点恢复']
        }
