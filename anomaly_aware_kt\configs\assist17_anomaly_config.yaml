# ASSISTments 2017专用异常生成配置

# 数据集基本信息
dataset_info:
  name: "ASSISTments 2017"
  n_questions: 102
  n_pid: 3162
  avg_sequence_length: 551.9
  domain: "Mathematics"
  
  # 数据字段
  fields:
    q: "问题ID (0-101)"
    s: "答案 (0/1)"
    pid: "问题类型ID (0-3161)"
    it: "交互类型"
    at: "答题时间"

# 异常生成策略配置
anomaly_strategies:
  # 策略权重 (针对ASSIST17优化)
  strategy_weights:
    consecutive: 0.20        # 连续异常 - 疲劳、注意力不集中
    difficulty_based: 0.30   # 基于难度 - 猜测、失误
    skill_jump: 0.25         # 技能跳跃 - 知识跳跃、作弊
    time_anomaly: 0.15       # 时间异常 - 时间压力、查资料
    interaction_pattern: 0.10 # 交互模式异常 - 系统使用异常
  
  # 各策略详细配置
  consecutive:
    name: "连续异常"
    description: "模拟疲劳、注意力不集中等导致的连续错误"
    parameters:
      min_length: 3
      max_length: 12
      flip_probability: 0.8
      fatigue_threshold: 0.7  # 序列70%位置开始疲劳
    
  difficulty_based:
    name: "基于难度的异常"
    description: "基于问题难度的猜测和失误行为"
    parameters:
      easy_threshold: 0.3     # 简单题阈值
      hard_threshold: 0.7     # 困难题阈值
      guess_probability: 0.6  # 困难题猜对概率
      slip_probability: 0.7   # 简单题失误概率
      use_pid_difficulty: true # 使用问题类型ID估算难度
    
  skill_jump:
    name: "技能跳跃异常"
    description: "跳过前置技能直接掌握高级技能"
    parameters:
      min_skills: 3           # 最少技能数量
      jump_probability: 0.4   # 跳跃概率
      advanced_skill_ratio: 0.5 # 高级技能比例
      prerequisite_check: true # 检查前置关系
    
  time_anomaly:
    name: "时间相关异常"
    description: "基于答题时间的异常行为"
    parameters:
      fast_threshold: 0.3     # 快速答题阈值 (中位数的30%)
      slow_threshold: 3.0     # 慢速答题阈值 (中位数的3倍)
      fast_random_prob: 0.5   # 快速答题随机化概率
      slow_flip_prob: 0.3     # 慢速答题翻转概率
      use_position_proxy: true # 无时间信息时使用位置代理
    
  interaction_pattern:
    name: "交互模式异常"
    description: "基于交互类型的异常模式"
    parameters:
      min_interactions: 2     # 最少交互类型数
      anomaly_count: 3        # 每种交互类型的异常数量
      pattern_flip_prob: 1.0  # 模式翻转概率

# 质量控制配置
quality_control:
  # 异常密度控制
  density:
    min_anomaly_ratio: 0.05   # 最小异常比例
    max_anomaly_ratio: 0.25   # 最大异常比例
    target_anomaly_ratio: 0.15 # 目标异常比例
    min_density_per_sequence: 0.1 # 每个异常序列的最小密度
  
  # 认知一致性检查
  cognitive_consistency:
    max_difficulty_jump: 0.4  # 最大难度跳跃
    min_learning_progression: -0.2 # 最小学习进展
    skill_mastery_threshold: 0.6 # 技能掌握阈值
  
  # 时间一致性检查
  temporal_consistency:
    max_time_variance: 5.0    # 最大时间方差倍数
    fatigue_progression: true # 疲劳递进检查
    session_boundary_respect: true # 尊重会话边界

# 数据集特定优化
assist17_optimizations:
  # 问题难度估算
  difficulty_estimation:
    method: "historical_accuracy" # 基于历史正确率
    min_samples: 5              # 最少样本数
    smoothing_factor: 0.1       # 平滑因子
    fallback_method: "id_based" # 备用方法：基于ID
  
  # 技能关系推断
  skill_relationships:
    method: "co_occurrence"     # 共现分析
    min_co_occurrence: 10       # 最小共现次数
    correlation_threshold: 0.3  # 相关性阈值
    build_prerequisite_graph: true # 构建前置关系图
  
  # 时间特征处理
  time_features:
    normalize_by_median: true   # 按中位数归一化
    detect_sessions: true       # 检测学习会话
    session_gap_threshold: 3600 # 会话间隔阈值(秒)
    handle_missing_time: "position_proxy" # 缺失时间处理

# 阶段特定配置
stage_specific:
  # 检测器训练阶段
  detector_training:
    anomaly_ratio: 0.15
    strategy_weights:
      consecutive: 0.25
      difficulty_based: 0.35
      skill_jump: 0.20
      time_anomaly: 0.15
      interaction_pattern: 0.05
    ensure_diversity: true      # 确保异常多样性
    min_anomaly_per_batch: 2    # 每批次最少异常数
  
  # 异常感知训练阶段
  anomaly_aware_training:
    anomaly_ratio: 0.12         # 稍低的异常比例
    strategy_weights:
      consecutive: 0.20
      difficulty_based: 0.30
      skill_jump: 0.25
      time_anomaly: 0.15
      interaction_pattern: 0.10
    curriculum_progression:     # 课程学习中的异常强度
      foundation: 0.08          # 基础阶段
      integration: 0.12         # 整合阶段
      refinement: 0.15          # 精化阶段
  
  # 评估阶段
  evaluation:
    test_ratios: [0.05, 0.10, 0.15, 0.20] # 测试不同异常比例
    strategy_weights:           # 评估时的策略权重
      consecutive: 0.20
      difficulty_based: 0.30
      skill_jump: 0.25
      time_anomaly: 0.15
      interaction_pattern: 0.10
    include_clean_test: true    # 包含干净测试集

# 实验配置
experimental:
  # 可重现性
  reproducibility:
    random_seed: 42
    deterministic_generation: true
    version_control: true
  
  # 统计分析
  statistics:
    compute_dataset_stats: true  # 计算数据集统计
    save_generation_log: true    # 保存生成日志
    validate_quality: true       # 验证生成质量
  
  # 调试选项
  debugging:
    visualize_anomalies: false   # 可视化异常
    save_intermediate_results: false # 保存中间结果
    verbose_logging: false       # 详细日志

# 使用示例配置
usage_examples:
  # 快速测试
  quick_test:
    anomaly_ratio: 0.10
    strategies: ["consecutive", "difficulty_based"]
    quality_check: false
  
  # 标准训练
  standard_training:
    anomaly_ratio: 0.15
    strategies: ["consecutive", "difficulty_based", "skill_jump"]
    quality_check: true
  
  # 高质量研究
  research_quality:
    anomaly_ratio: 0.15
    strategies: ["consecutive", "difficulty_based", "skill_jump", "time_anomaly", "interaction_pattern"]
    quality_check: true
    compute_statistics: true
    validate_consistency: true

# 性能优化
performance:
  # 批处理优化
  batch_processing:
    parallel_generation: true   # 并行生成
    chunk_size: 1000           # 块大小
    memory_efficient: true     # 内存高效模式
  
  # 缓存策略
  caching:
    cache_difficulty_scores: true # 缓存难度分数
    cache_skill_relationships: true # 缓存技能关系
    cache_statistics: true      # 缓存统计信息
