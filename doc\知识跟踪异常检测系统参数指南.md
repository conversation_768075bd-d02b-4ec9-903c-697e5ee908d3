# 知识跟踪异常检测系统参数指南

## 一、概述
本文件说明知识跟踪异常检测系统的核心参数设计逻辑、默认值合理性及调整策略，帮助用户根据实际业务场景优化配置。文档覆盖规则参数（如连续答对阈值）和融合参数（如异常分阈值），并提供数据验证方法。


## 二、核心参数说明

### （一）规则参数（单条异常检测逻辑）

#### 1. 规则1：连续答对后错误（模式异常）
**参数**：`streak_threshold`（连续答对次数阈值）  
**默认值**：3  
**设计逻辑**：  
连续答对3次是教育心理学中“稳定掌握”的经验阈值（单次/两次正确可能是随机），突然答错更可能是外部干扰或数据异常。  

| 调整场景                | 建议值 | 说明                          |  
|-------------------------|--------|-------------------------------|  
| 严格防作弊（如考试系统）| 2      | 捕捉更早的异常模式            |  
| 日常练习（容忍波动）    | 4      | 减少正常错误的误判            |  


#### 2. 规则2：知识掌握骤降（知识异常）
**参数**：`drop_window`（近期正确率窗口大小）、`drop_threshold`（正确率降幅阈值）  
**默认值**：`drop_window=5`, `drop_threshold=0.3`  
**设计逻辑**：  
- `drop_window=5`：短期学习效果的最小观测窗口（<5次无法区分波动与真实变化）；  
- `drop_threshold=0.3`：30%的降幅是教育领域“显著变化”的经验阈值（如历史80%→近期50%）。  

| 业务场景          | `drop_window`调整 | `drop_threshold`调整 | 说明                      |  
|-------------------|-------------------|----------------------|---------------------------|  
| 周测（短序列）    | 3                 | 0.25                 | 适配短序列，敏感捕捉变化  |  
| 学期考试（长序列）| 7                 | 0.35                 | 平滑长期趋势，减少误判    |  


#### 3. 规则3：低效率重复（效率异常）
**参数**：`repeat_threshold`（重复答题次数阈值）、`low_acc_threshold`（低正确率阈值）  
**默认值**：`repeat_threshold=5`, `low_acc_threshold=0.4`  
**设计逻辑**：  
- `repeat_threshold=5`：超过5次仍未掌握通常属于无效练习（教育中3-4次即可掌握）；  
- `low_acc_threshold=0.4`：40%以下正确率表明基本未掌握（>40%可能接近临界点）。  

| 业务目标          | `repeat_threshold` | `low_acc_threshold` | 说明                      |  
|-------------------|--------------------|---------------------|---------------------------|  
| 识别无效练习      | 4                  | 0.45                | 提前预警，提高低正确率标准|  
| 容忍必要练习      | 6                  | 0.35                | 放宽异常条件，减少误判    |  


#### 4. 规则4：全对/全错序列（行为异常）
**参数**：`same_window`（连续全对/全错窗口长度）、`min_skills`（涉及知识点数阈值）  
**默认值**：`same_window=8`, `min_skills=3`  
**设计逻辑**：  
- `same_window=8`：8次是“异常行为序列”的最小长度（短于8次可能是偶然）；  
- `min_skills=3`：涉及3+知识点的全对/全错更可能是作弊或系统异常。  

| 业务场景          | `same_window`调整 | `min_skills`调整 | 说明                      |  
|-------------------|-------------------|------------------|---------------------------|  
| 防作弊（考试系统）| 6                 | 4                | 提高敏感度，捕捉短序列异常|  
| 日常练习          | 10                | 2                | 降低误判，允许偶然全对    |  


#### 5. 规则5：知识点跳跃（逻辑异常）
**参数**：`mastery_threshold`（前置知识点掌握阈值）  
**默认值**：0.7（正确率≥70%视为掌握）  
**设计逻辑**：  
70%是教育领域“基本掌握”的常见标准（如考试70分视为“良好”），未掌握前置却答对高难度知识点可能是数据异常。  

| 知识体系关联性    | 建议值 | 说明                      |  
|-------------------|--------|---------------------------|  
| 强关联（如数学）  | 0.75   | 确保前置知识扎实          |  
| 弱关联（如语言）  | 0.6    | 允许灵活掌握              |  


#### 6. 规则6：异常序列长度（统计异常）
**参数**：`sigma`（标准差倍数阈值）  
**默认值**：2.0（超过2倍标准差视为异常）  
**设计逻辑**：  
统计学中，2倍标准差外的数据属于小概率事件（约5%），可作为异常筛选的合理阈值。  

| 数据分布特征      | 建议值 | 说明                      |  
|-------------------|--------|---------------------------|  
| 长度方差大（跨年级）| 1.5    | 捕捉更多极端值            |  
| 长度方差小（同年级）| 2.5    | 减少正常波动的误判        |  


### （二）融合参数（多规则综合判定）

#### 1. 规则权重（`rule_weights`）
**默认值**：`[0.3, 0.3, 0.2, 0.1, 0.1, 0.0]`（规则1-6权重）  
**设计逻辑**：  
- 规则1（模式异常）和规则2（知识异常）是核心，权重最高（各30%）；  
- 规则6（序列长度）与知识掌握弱相关，默认权重0。  

| 业务重点          | 调整示例                | 说明                      |  
|-------------------|-------------------------|---------------------------|  
| 关注知识稳定性    | `[0.2, 0.4, 0.2, 0.1, 0.1, 0.0]` | 提高规则2权重            |  
| 关注学习效率      | `[0.2, 0.2, 0.3, 0.1, 0.1, 0.1]` | 提高规则3权重            |  


#### 2. 融合阈值（`fusion_threshold`）
**默认值**：0.6（总异常分≥0.6标记为异常）  
**设计逻辑**：  
平衡召回与精确（需至少两个中权重规则同时触发），避免过拟合。  

| 业务目标          | 建议值 | 说明                      |  
|-------------------|--------|---------------------------|  
| 高召回（挖掘异常）| 0.5    | 捕捉更多潜在异常          |  
| 高精度（人工复核）| 0.7    | 减少误判样本              |  


## 三、参数验证与优化流程
### 1. 数据统计分析  
通过历史数据验证参数合理性，示例：  
```python
# 统计规则1的触发频率（连续答对3次后错误的样本占比）
import pandas as pd
data = pd.read_json("data/labeled_data.json")
streak_errors = data["anomaly_label"].apply(lambda x: sum(x))
print(f"规则1触发占比：{streak_errors.mean():.2%}")  # 正常应<5%