# 异常检测器训练策略配置文件
# 支持三种训练策略：basic, enhanced, aggressive

# 默认策略
default_strategy: "enhanced"

# 策略配置
strategies:
  basic:
    name: "Basic Strategy"
    description: "基础策略 - 标准训练流程"
    
    # 训练参数
    training:
      epochs: 30
      learning_rate: 1e-3
      batch_size: 32
      patience: 10
      
    # 优化器配置
    optimizer:
      type: "adam"
      weight_decay: 1e-5
      betas: [0.9, 0.999]
      
    # 学习率调度器
    scheduler:
      type: "step_lr"
      step_size: 10
      gamma: 0.5
      
    # 损失函数
    loss:
      type: "bce"
      class_weights: "balanced"
      
    # 异常生成
    anomaly:
      ratio: 0.1
      types: ["consecutive", "difficulty_based"]
      dynamic_adjustment: false
      
    # 评估指标
    evaluation:
      optimize_for: "f1_score"
      metrics: ["f1_score", "auc_roc", "precision", "recall"]
      
  enhanced:
    name: "Enhanced Strategy"
    description: "增强策略 - 课程学习 + 动态调整"
    
    # 训练参数
    training:
      epochs: 50
      learning_rate: 1e-3
      batch_size: 32
      patience: 15
      
    # 优化器配置
    optimizer:
      type: "adamw"
      weight_decay: 1e-4
      betas: [0.9, 0.999]
      
    # 学习率调度器
    scheduler:
      type: "cosine_annealing"
      T_max: 50
      eta_min: 1e-6
      
    # 损失函数
    loss:
      type: "focal"
      alpha: 0.25
      gamma: 2.0
      
    # 课程学习
    curriculum:
      enabled: true
      stages:
        - stage: "easy"
          epochs: [1, 15]
          anomaly_ratio: 0.05
          difficulty: "low"
        - stage: "medium"
          epochs: [16, 35]
          anomaly_ratio: 0.10
          difficulty: "medium"
        - stage: "hard"
          epochs: [36, 50]
          anomaly_ratio: 0.15
          difficulty: "high"
          
    # 异常生成
    anomaly:
      ratio: 0.10
      types: ["consecutive", "difficulty_based", "pattern", "random_burst"]
      dynamic_adjustment: true
      min_ratio: 0.05
      max_ratio: 0.20
      
    # 评估指标
    evaluation:
      optimize_for: "f1_score"
      metrics: ["f1_score", "auc_roc", "precision", "recall", "balanced_accuracy"]
      
  aggressive:
    name: "Aggressive Strategy"
    description: "激进策略 - 极端不平衡处理"
    
    # 训练参数
    training:
      epochs: 60
      learning_rate: 1e-3
      batch_size: 16
      patience: 20
      
    # 优化器配置
    optimizer:
      type: "adamw"
      weight_decay: 1e-3
      betas: [0.9, 0.999]
      
    # 学习率调度器
    scheduler:
      type: "reduce_on_plateau"
      mode: "max"
      factor: 0.5
      patience: 5
      min_lr: 1e-6
      
    # 损失函数
    loss:
      type: "multi_loss"
      components:
        bce:
          weight: 0.3
          pos_weight: 10.0
        focal:
          weight: 0.4
          alpha: 0.75
          gamma: 3.0
        dice:
          weight: 0.2
        tversky:
          weight: 0.1
          alpha: 0.3
          beta: 0.7
          
    # 异常生成
    anomaly:
      ratio: 0.25
      types: ["consecutive", "difficulty_based", "pattern", "random_burst", "extreme"]
      dynamic_adjustment: true
      min_ratio: 0.15
      max_ratio: 0.50
      force_batch_balance: true
      
    # 数据增强
    augmentation:
      enabled: true
      techniques: ["noise_injection", "sequence_permutation", "masking"]
      
    # 评估指标
    evaluation:
      optimize_for: "recall"
      metrics: ["f1_score", "auc_roc", "precision", "recall", "balanced_accuracy", "mcc"]
      
# 全局配置
global:
  device: "auto"
  random_seed: 42
  
  logging:
    level: "INFO"
    save_logs: true
    log_interval: 10
    
  checkpoints:
    save_best: true
    save_interval: 10
    keep_last_n: 3
    
  early_stopping:
    enabled: true
    monitor: "val_f1_score"
    mode: "max"
    
# 数据集特定配置
dataset_specific:
  assist09:
    recommended_strategy: "enhanced"
    batch_size_override: 16
    
  assist17:
    recommended_strategy: "enhanced"
    batch_size_override: 32
    
  algebra05:
    recommended_strategy: "basic"
    batch_size_override: 64
    
  statics:
    recommended_strategy: "aggressive"
    batch_size_override: 16
