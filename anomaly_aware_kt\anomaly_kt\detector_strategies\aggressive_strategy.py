"""
Aggressive异常检测器训练策略

激进策略专门处理极端的类别不平衡问题：
- 极端类别权重
- 强制批次平衡
- 多损失函数组合
- 动态学习率调整
- 数据重采样
- 集成多种正则化技术
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Optional, Any
from collections import Counter

from .base_strategy import BaseDetectorStrategy


class AggressiveDetectorStrategy(BaseDetectorStrategy):
    """Aggressive异常检测器训练策略"""
    
    def __init__(self, model: nn.Module, device: str, save_dir: str, patience: int = 15):
        super().__init__(model, device, save_dir, patience)
        
        # Aggressive策略特有的配置
        self.force_batch_balance = True
        self.use_extreme_weights = True
        self.use_multiple_losses = True
        self.use_dynamic_sampling = True
        
        # 动态参数
        self.min_anomaly_ratio = 0.2
        self.max_anomaly_ratio = 0.5
        self.extreme_pos_weight = 10.0
        
        # 损失函数权重
        self.loss_weights = {
            'bce': 0.3,
            'focal': 0.4,
            'dice': 0.2,
            'tversky': 0.1
        }
        
    def _get_strategy_config(self) -> Dict:
        """获取Aggressive策略配置"""
        return {
            'strategy_type': 'aggressive',
            'loss_function': 'multi_loss_ensemble',
            'optimizer': 'adamw_with_lookahead',
            'scheduler': 'reduce_on_plateau',
            'anomaly_ratio': 'dynamic_extreme',
            'data_augmentation': 'heavy_augmentation',
            'class_weights': 'extreme_weights',
            'batch_balancing': True,
            'dynamic_sampling': True,
            'regularization': 'multiple'
        }
        
    def _create_optimizer(self, learning_rate: float) -> torch.optim.Optimizer:
        """创建带Lookahead的AdamW优化器"""
        base_optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-3,  # 更强的正则化
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 如果有Lookahead优化器，可以在这里包装
        # 这里简化为直接返回AdamW
        return base_optimizer
        
    def _create_scheduler(self, optimizer: torch.optim.Optimizer, epochs: int) -> Optional[Any]:
        """创建ReduceLROnPlateau调度器"""
        return torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='max',  # 监控F1分数最大化
            factor=0.5,
            patience=5,
            verbose=True,
            min_lr=1e-6
        )
        
    def _calculate_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                       epoch: int, total_epochs: int) -> torch.Tensor:
        """计算多损失函数组合"""
        
        losses = {}
        
        # 1. BCE Loss with extreme weights
        if 'bce' in self.loss_weights:
            pos_weight = self._calculate_extreme_weights(targets)
            bce_loss = F.binary_cross_entropy_with_logits(
                outputs, targets.float(), pos_weight=pos_weight
            )
            losses['bce'] = bce_loss
            
        # 2. Focal Loss
        if 'focal' in self.loss_weights:
            focal_loss = self._focal_loss(outputs, targets, gamma=3.0, alpha=0.75)
            losses['focal'] = focal_loss
            
        # 3. Dice Loss
        if 'dice' in self.loss_weights:
            dice_loss = self._dice_loss(outputs, targets)
            losses['dice'] = dice_loss
            
        # 4. Tversky Loss
        if 'tversky' in self.loss_weights:
            tversky_loss = self._tversky_loss(outputs, targets, alpha=0.3, beta=0.7)
            losses['tversky'] = tversky_loss
            
        # 组合损失
        total_loss = sum(
            self.loss_weights[name] * loss 
            for name, loss in losses.items() 
            if name in self.loss_weights
        )
        
        # 添加正则化项
        reg_loss = self._regularization_loss()
        total_loss += 0.01 * reg_loss
        
        return total_loss
        
    def _calculate_extreme_weights(self, targets: torch.Tensor) -> torch.Tensor:
        """计算极端类别权重"""
        pos_count = targets.sum().item()
        neg_count = (targets == 0).sum().item()
        
        if pos_count == 0:
            return torch.tensor(self.extreme_pos_weight, device=self.device)
            
        # 极端权重：至少10倍，最多100倍
        ratio = neg_count / pos_count
        extreme_weight = max(self.extreme_pos_weight, min(100.0, ratio * 2))
        
        return torch.tensor(extreme_weight, device=self.device)
        
    def _focal_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                   gamma: float = 3.0, alpha: float = 0.75) -> torch.Tensor:
        """Focal Loss with aggressive parameters"""
        probs = torch.sigmoid(outputs)
        ce_loss = F.binary_cross_entropy_with_logits(outputs, targets.float(), reduction='none')
        
        p_t = probs * targets + (1 - probs) * (1 - targets)
        modulating_factor = (1 - p_t) ** gamma
        
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)
        focal_loss = alpha_t * modulating_factor * ce_loss
        
        return focal_loss.mean()
        
    def _dice_loss(self, outputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """Dice Loss for better overlap"""
        probs = torch.sigmoid(outputs)
        smooth = 1e-8
        
        intersection = (probs * targets).sum()
        union = probs.sum() + targets.sum()
        
        dice = (2 * intersection + smooth) / (union + smooth)
        return 1 - dice
        
    def _tversky_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                     alpha: float = 0.3, beta: float = 0.7) -> torch.Tensor:
        """Tversky Loss for handling false positives and negatives"""
        probs = torch.sigmoid(outputs)
        smooth = 1e-8
        
        true_pos = (probs * targets).sum()
        false_neg = (targets * (1 - probs)).sum()
        false_pos = ((1 - targets) * probs).sum()
        
        tversky = (true_pos + smooth) / (true_pos + alpha * false_neg + beta * false_pos + smooth)
        return 1 - tversky
        
    def _regularization_loss(self) -> torch.Tensor:
        """多种正则化损失"""
        reg_loss = 0
        
        # L1正则化
        l1_reg = sum(p.abs().sum() for p in self.model.parameters())
        
        # L2正则化
        l2_reg = sum(p.pow(2).sum() for p in self.model.parameters())
        
        # 组合正则化
        reg_loss = 0.01 * l1_reg + 0.001 * l2_reg
        
        return reg_loss
        
    def _adjust_training_params(self, epoch: int, total_epochs: int,
                               current_performance: Dict) -> Dict:
        """激进的动态参数调整"""
        progress = epoch / total_epochs
        
        # 基础参数
        params = {
            'anomaly_ratio': self._calculate_dynamic_anomaly_ratio(progress, current_performance),
            'grad_clip': 0.5,  # 更严格的梯度裁剪
            'dropout_rate': 0.3 + 0.2 * progress,  # 渐进增加dropout
        }
        
        # 基于性能激进调整
        if current_performance:
            f1_score = current_performance.get('f1_score', 0)
            precision = current_performance.get('precision', 0)
            recall = current_performance.get('recall', 0)
            
            # 激进的异常比例调整
            if recall < 0.5:  # 召回率太低
                params['anomaly_ratio'] = min(0.6, params['anomaly_ratio'] * 1.5)
                self.extreme_pos_weight = min(200.0, self.extreme_pos_weight * 1.2)
                
            elif precision < 0.3:  # 精确率太低
                params['anomaly_ratio'] = max(0.15, params['anomaly_ratio'] * 0.7)
                self.extreme_pos_weight = max(5.0, self.extreme_pos_weight * 0.8)
                
            # 动态调整损失权重
            if f1_score < 0.3:
                # 性能很差，增加Focal Loss权重
                self.loss_weights['focal'] = min(0.6, self.loss_weights['focal'] * 1.1)
                self.loss_weights['bce'] = max(0.1, self.loss_weights['bce'] * 0.9)
                
        return params
        
    def _calculate_dynamic_anomaly_ratio(self, progress: float, performance: Dict) -> float:
        """计算动态异常比例"""
        # 基础比例：从高到低再到中等
        if progress < 0.3:
            base_ratio = self.max_anomaly_ratio  # 开始时用高比例
        elif progress < 0.7:
            base_ratio = self.min_anomaly_ratio  # 中期用低比例
        else:
            base_ratio = (self.min_anomaly_ratio + self.max_anomaly_ratio) / 2  # 后期用中等比例
            
        # 基于性能微调
        if performance:
            recall = performance.get('recall', 0.5)
            if recall < 0.4:
                base_ratio *= 1.3  # 召回率低，增加异常比例
            elif recall > 0.8:
                base_ratio *= 0.8  # 召回率高，减少异常比例
                
        return np.clip(base_ratio, 0.1, 0.6)
        
    def _train_epoch(self, train_loader, optimizer, epoch: int, total_epochs: int,
                    anomaly_ratio: float, adjusted_params: Dict) -> Dict:
        """重写训练epoch以支持强制批次平衡"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, batch in enumerate(train_loader):
            q, s, pid = self._get_batch_data(batch)
            
            # 生成异常数据
            current_ratio = adjusted_params.get('anomaly_ratio', anomaly_ratio)
            s_anomaly, anomaly_labels = self.generator.generate_anomalies(
                q, s, pid, ratio=current_ratio
            )
            
            # 强制批次平衡（如果启用）
            if self.force_batch_balance:
                s_anomaly, anomaly_labels = self._force_batch_balance(s_anomaly, anomaly_labels)
                
            # 前向传播
            optimizer.zero_grad()
            outputs = self.model(q, s_anomaly, pid)
            
            # 计算损失
            loss = self._calculate_loss(outputs, anomaly_labels, epoch, total_epochs)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), adjusted_params.get('grad_clip', 0.5))
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
        return {
            'loss': total_loss / num_batches,
            'anomaly_ratio': adjusted_params.get('anomaly_ratio', anomaly_ratio),
            'extreme_pos_weight': self.extreme_pos_weight
        }
        
    def _force_batch_balance(self, s_anomaly: torch.Tensor, anomaly_labels: torch.Tensor) -> tuple:
        """强制批次平衡"""
        # 简化实现：确保每个批次至少有30%的异常样本
        batch_size = s_anomaly.size(0)
        seq_len = s_anomaly.size(1)
        
        current_anomaly_count = anomaly_labels.sum().item()
        target_anomaly_count = max(int(batch_size * seq_len * 0.3), current_anomaly_count)
        
        if current_anomaly_count < target_anomaly_count:
            # 需要添加更多异常
            additional_needed = target_anomaly_count - current_anomaly_count
            
            # 随机选择位置添加异常
            normal_positions = (anomaly_labels == 0).nonzero(as_tuple=False)
            if len(normal_positions) > 0:
                selected_indices = torch.randperm(len(normal_positions))[:additional_needed]
                selected_positions = normal_positions[selected_indices]
                
                for pos in selected_positions:
                    batch_idx, seq_idx = pos[0], pos[1]
                    anomaly_labels[batch_idx, seq_idx] = 1
                    # 简单的异常注入：翻转答案
                    s_anomaly[batch_idx, seq_idx] = 1 - s_anomaly[batch_idx, seq_idx]
                    
        return s_anomaly, anomaly_labels
        
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "Aggressive Strategy"
        
    def get_detailed_info(self) -> Dict:
        """获取详细信息"""
        return {
            'strategy_name': self.get_strategy_name(),
            'description': 'Aggressive异常检测器训练策略',
            'features': [
                '极端类别权重 (10-100倍)',
                '强制批次平衡',
                '多损失函数组合 (BCE+Focal+Dice+Tversky)',
                '动态异常比例 (20%-50%)',
                '激进参数调整',
                '多重正则化',
                '严格梯度裁剪'
            ],
            'advantages': [
                '处理极端不平衡效果最好',
                '召回率通常很高',
                '对困难样本敏感',
                '自适应能力极强'
            ],
            'limitations': [
                '容易过拟合',
                '训练不稳定',
                '计算开销最大',
                '精确率可能较低',
                '需要大量调试'
            ],
            'best_use_cases': [
                '极端类别不平衡 (>1:100)',
                '召回率要求极高',
                '有充足计算资源和时间',
                '数据量较大'
            ],
            'loss_composition': self.loss_weights,
            'hyperparameters': {
                'learning_rate': '1e-3 (推荐)',
                'weight_decay': '1e-3',
                'extreme_pos_weight': f'{self.extreme_pos_weight}',
                'anomaly_ratio': f'{self.min_anomaly_ratio}-{self.max_anomaly_ratio}',
                'grad_clip': '0.5',
                'patience': '15'
            }
        }
        
    def set_extreme_weights(self, enabled: bool, weight: float = 10.0):
        """设置极端权重"""
        self.use_extreme_weights = enabled
        self.extreme_pos_weight = weight
        
    def set_batch_balancing(self, enabled: bool):
        """设置强制批次平衡"""
        self.force_batch_balance = enabled
        
    def customize_loss_weights(self, weights: Dict[str, float]):
        """自定义损失权重"""
        self.loss_weights.update(weights)
