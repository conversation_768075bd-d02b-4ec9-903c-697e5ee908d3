"""
Enhanced异常检测器训练策略

增强策略使用更复杂的训练技术：
- 课程学习：从简单到复杂的异常
- 动态异常比例调整
- Focal Loss处理类别不平衡
- 对比学习增强特征表示
- 自适应学习率调度
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Optional, Any

from .base_strategy import BaseDetectorStrategy


class EnhancedDetectorStrategy(BaseDetectorStrategy):
    """Enhanced异常检测器训练策略"""
    
    def __init__(self, model: nn.Module, device: str, save_dir: str, patience: int = 10):
        super().__init__(model, device, save_dir, patience)
        
        # Enhanced策略特有的组件
        self.use_curriculum = True
        self.use_focal_loss = True
        self.use_contrastive_loss = True
        
        # 课程学习阶段
        self.curriculum_stages = [
            {'end_ratio': 0.3, 'anomaly_ratio': 0.05, 'focal_gamma': 1.0, 'difficulty': 'easy'},
            {'end_ratio': 0.6, 'anomaly_ratio': 0.10, 'focal_gamma': 1.5, 'difficulty': 'medium'},
            {'end_ratio': 1.0, 'anomaly_ratio': 0.15, 'focal_gamma': 2.0, 'difficulty': 'hard'}
        ]
        
    def _get_strategy_config(self) -> Dict:
        """获取Enhanced策略配置"""
        return {
            'strategy_type': 'enhanced',
            'loss_function': 'focal + contrastive',
            'optimizer': 'adamw',
            'scheduler': 'cosine_annealing',
            'anomaly_ratio': 'dynamic_curriculum',
            'data_augmentation': 'advanced',
            'class_weights': 'focal_based',
            'curriculum_learning': True,
            'contrastive_learning': True
        }
        
    def _create_optimizer(self, learning_rate: float) -> torch.optim.Optimizer:
        """创建AdamW优化器"""
        return torch.optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-4,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
    def _create_scheduler(self, optimizer: torch.optim.Optimizer, epochs: int) -> Optional[Any]:
        """创建余弦退火调度器"""
        return torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=epochs,
            eta_min=1e-6
        )
        
    def _calculate_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                       epoch: int, total_epochs: int) -> torch.Tensor:
        """计算组合损失：Focal Loss + Contrastive Loss"""
        
        # 1. Focal Loss
        focal_loss = self._focal_loss(outputs, targets, epoch, total_epochs)
        
        # 2. Contrastive Loss (如果启用)
        contrastive_loss = 0
        if self.use_contrastive_loss:
            contrastive_loss = self._contrastive_loss(outputs, targets)
            
        # 3. 组合损失
        # 动态调整权重：早期更注重focal loss，后期增加contrastive loss
        progress = epoch / total_epochs
        focal_weight = 1.0
        contrastive_weight = progress * 0.3  # 最大权重0.3
        
        total_loss = focal_weight * focal_loss + contrastive_weight * contrastive_loss
        
        return total_loss
        
    def _focal_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                   epoch: int, total_epochs: int) -> torch.Tensor:
        """Focal Loss实现"""
        # 获取当前阶段的gamma值
        current_stage = self._get_current_curriculum_stage(epoch, total_epochs)
        gamma = current_stage['focal_gamma']
        
        # 计算概率
        probs = torch.sigmoid(outputs)
        
        # 计算focal loss
        ce_loss = F.binary_cross_entropy_with_logits(outputs, targets.float(), reduction='none')
        
        # 计算调制因子
        p_t = probs * targets + (1 - probs) * (1 - targets)
        modulating_factor = (1 - p_t) ** gamma
        
        # 类别权重
        alpha = self._calculate_alpha_weights(targets)
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)
        
        focal_loss = alpha_t * modulating_factor * ce_loss
        
        return focal_loss.mean()
        
    def _contrastive_loss(self, outputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """对比学习损失"""
        # 获取特征表示（假设模型有get_features方法）
        if hasattr(self.model, 'get_features'):
            features = self.model.get_features()
        else:
            # 如果没有特征提取方法，使用输出作为特征
            features = outputs
            
        # 简化的对比损失：拉近同类样本，推远异类样本
        batch_size = features.size(0)
        
        # 计算特征相似度矩阵
        features_norm = F.normalize(features, dim=1)
        similarity_matrix = torch.matmul(features_norm, features_norm.T)
        
        # 创建标签相似度矩阵
        targets_expanded = targets.unsqueeze(1)
        label_similarity = (targets_expanded == targets_expanded.T).float()
        
        # 对比损失：同类样本相似度高，异类样本相似度低
        positive_pairs = label_similarity * similarity_matrix
        negative_pairs = (1 - label_similarity) * similarity_matrix
        
        # 计算损失
        positive_loss = -torch.log(torch.sigmoid(positive_pairs) + 1e-8)
        negative_loss = -torch.log(torch.sigmoid(-negative_pairs) + 1e-8)
        
        # 只考虑非对角线元素
        mask = (1 - torch.eye(batch_size, device=self.device))
        positive_loss = (positive_loss * mask).sum() / (label_similarity * mask).sum().clamp(min=1)
        negative_loss = (negative_loss * mask).sum() / ((1 - label_similarity) * mask).sum().clamp(min=1)
        
        return positive_loss + negative_loss
        
    def _calculate_alpha_weights(self, targets: torch.Tensor) -> torch.Tensor:
        """计算Focal Loss的alpha权重"""
        pos_count = targets.sum().item()
        neg_count = (targets == 0).sum().item()
        
        if pos_count == 0:
            return torch.tensor(0.5, device=self.device)
            
        # 动态alpha：少数类权重更高
        total_count = pos_count + neg_count
        alpha = neg_count / total_count
        
        return torch.tensor(alpha, device=self.device)
        
    def _get_current_curriculum_stage(self, epoch: int, total_epochs: int) -> Dict:
        """获取当前课程学习阶段"""
        progress = epoch / total_epochs
        
        for stage in self.curriculum_stages:
            if progress <= stage['end_ratio']:
                return stage
                
        # 如果超出所有阶段，返回最后一个阶段
        return self.curriculum_stages[-1]
        
    def _adjust_training_params(self, epoch: int, total_epochs: int,
                               current_performance: Dict) -> Dict:
        """动态调整训练参数"""
        # 获取当前课程阶段
        current_stage = self._get_current_curriculum_stage(epoch, total_epochs)
        
        # 基础参数
        params = {
            'anomaly_ratio': current_stage['anomaly_ratio'],
            'focal_gamma': current_stage['focal_gamma'],
            'difficulty': current_stage['difficulty'],
            'grad_clip': 1.0
        }
        
        # 基于性能动态调整
        if current_performance:
            f1_score = current_performance.get('f1_score', 0)
            precision = current_performance.get('precision', 0)
            recall = current_performance.get('recall', 0)
            
            # 如果精确率低，减少异常比例
            if precision < 0.6 and f1_score > 0:
                params['anomaly_ratio'] *= 0.8
                
            # 如果召回率低，增加异常比例
            elif recall < 0.6 and f1_score > 0:
                params['anomaly_ratio'] *= 1.2
                
            # 限制异常比例范围
            params['anomaly_ratio'] = np.clip(params['anomaly_ratio'], 0.05, 0.3)
            
        return params
        
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "Enhanced Strategy"
        
    def get_detailed_info(self) -> Dict:
        """获取详细信息"""
        return {
            'strategy_name': self.get_strategy_name(),
            'description': 'Enhanced异常检测器训练策略',
            'features': [
                '课程学习：从简单到复杂',
                'Focal Loss处理类别不平衡',
                '对比学习增强特征表示',
                '动态异常比例调整',
                'AdamW优化器',
                '余弦退火学习率调度',
                '自适应参数调整'
            ],
            'advantages': [
                '处理类别不平衡效果好',
                '训练过程更稳定',
                '特征表示质量高',
                '自适应能力强',
                '收敛速度快'
            ],
            'limitations': [
                '计算开销较大',
                '超参数较多',
                '需要更多调试',
                '对数据质量敏感'
            ],
            'best_use_cases': [
                '中等规模的不平衡数据',
                '需要高质量特征表示',
                '有充足计算资源',
                '追求性能和稳定性平衡'
            ],
            'curriculum_stages': self.curriculum_stages,
            'hyperparameters': {
                'learning_rate': '1e-3 (推荐)',
                'weight_decay': '1e-4',
                'focal_gamma': '1.0 → 2.0 (课程学习)',
                'anomaly_ratio': '0.05 → 0.15 (动态调整)',
                'contrastive_weight': '0 → 0.3 (渐进增加)'
            }
        }
        
    def set_curriculum_learning(self, enabled: bool):
        """设置是否启用课程学习"""
        self.use_curriculum = enabled
        
    def set_contrastive_learning(self, enabled: bool):
        """设置是否启用对比学习"""
        self.use_contrastive_learning = enabled
        
    def customize_curriculum_stages(self, stages: list):
        """自定义课程学习阶段"""
        self.curriculum_stages = stages
