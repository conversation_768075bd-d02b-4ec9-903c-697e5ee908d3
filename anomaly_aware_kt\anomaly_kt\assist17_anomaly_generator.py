"""
ASSISTments 2017专用异常生成器

基于ASSIST17数据集的特征设计的专门异常生成器，
充分利用问题ID、问题类型、交互类型和时间信息。
"""

import torch
import numpy as np
from typing import Dict, Tuple, Optional, List
from collections import defaultdict, Counter
import random

from .generator import AnomalyGenerator


class ASSIST17AnomalyGenerator(AnomalyGenerator):
    """
    专门针对ASSISTments 2017数据集的异常生成器
    
    利用ASSIST17的丰富特征：
    - q: 问题ID (102个)
    - pid: 问题类型ID (3162个) 
    - it: 交互类型
    - at: 答题时间
    """
    
    def __init__(self, dataset_stats: Optional[Dict] = None, seed: Optional[int] = None):
        """
        初始化ASSIST17异常生成器
        
        Args:
            dataset_stats: 数据集统计信息（问题难度、类型分布等）
            seed: 随机种子
        """
        # ASSIST17特有的策略
        strategies = ['consecutive', 'difficulty_based', 'skill_jump', 'time_anomaly', 'interaction_pattern']
        super().__init__(strategies, seed)
        
        # 数据集统计信息
        self.dataset_stats = dataset_stats or {}
        self.question_difficulty = {}  # 问题难度映射
        self.skill_difficulty = {}     # 技能难度映射
        self.skill_prerequisites = {}  # 技能前置关系
        
        # 初始化统计信息
        self._initialize_stats()
    
    def _initialize_stats(self):
        """初始化数据集统计信息"""
        if 'question_difficulty' in self.dataset_stats:
            self.question_difficulty = self.dataset_stats['question_difficulty']
        
        if 'skill_difficulty' in self.dataset_stats:
            self.skill_difficulty = self.dataset_stats['skill_difficulty']
            
        if 'skill_prerequisites' in self.dataset_stats:
            self.skill_prerequisites = self.dataset_stats['skill_prerequisites']
    
    def generate_anomalies(self, 
                          q: torch.Tensor,
                          s: torch.Tensor, 
                          pid: Optional[torch.Tensor] = None,
                          it: Optional[torch.Tensor] = None,
                          at: Optional[torch.Tensor] = None,
                          anomaly_ratio: float = 0.15,
                          strategy_weights: Optional[Dict[str, float]] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        生成ASSIST17特定的异常
        
        Args:
            q: 问题ID序列 [batch_size, seq_len]
            s: 答案序列 [batch_size, seq_len]
            pid: 问题类型ID序列 [batch_size, seq_len]
            it: 交互类型序列 [batch_size, seq_len]
            at: 答题时间序列 [batch_size, seq_len]
            anomaly_ratio: 异常比例
            strategy_weights: 策略权重
            
        Returns:
            s_anomaly: 包含异常的答案序列
            anomaly_labels: 异常标签
        """
        batch_size, seq_len = s.shape
        s_anomaly = s.clone()
        anomaly_labels = torch.zeros_like(s)
        
        # ASSIST17特定的策略权重
        if strategy_weights is None:
            strategy_weights = {
                'consecutive': 0.20,      # 连续异常
                'difficulty_based': 0.30, # 基于难度的异常
                'skill_jump': 0.25,       # 技能跳跃异常
                'time_anomaly': 0.15,     # 时间相关异常
                'interaction_pattern': 0.10  # 交互模式异常
            }
        
        # 确定异常序列
        n_anomaly_seqs = max(1, int(batch_size * anomaly_ratio))
        anomaly_seq_indices = random.sample(range(batch_size), n_anomaly_seqs)
        
        # 归一化权重
        total_weight = sum(strategy_weights.values())
        strategy_probs = {k: v/total_weight for k, v in strategy_weights.items()}
        
        for idx in anomaly_seq_indices:
            # 选择策略
            strategy = np.random.choice(
                list(strategy_probs.keys()),
                p=list(strategy_probs.values())
            )
            
            # 应用策略
            if strategy == 'consecutive':
                s_anomaly[idx], anomaly_labels[idx] = self._consecutive_flip(s[idx], q[idx])
            elif strategy == 'difficulty_based':
                s_anomaly[idx], anomaly_labels[idx] = self._difficulty_based_flip_assist17(
                    s[idx], q[idx], pid[idx] if pid is not None else None
                )
            elif strategy == 'skill_jump':
                s_anomaly[idx], anomaly_labels[idx] = self._skill_jump_anomaly(
                    s[idx], q[idx], pid[idx] if pid is not None else None
                )
            elif strategy == 'time_anomaly':
                s_anomaly[idx], anomaly_labels[idx] = self._time_based_anomaly(
                    s[idx], q[idx], at[idx] if at is not None else None
                )
            elif strategy == 'interaction_pattern':
                s_anomaly[idx], anomaly_labels[idx] = self._interaction_pattern_anomaly(
                    s[idx], q[idx], it[idx] if it is not None else None
                )
        
        return s_anomaly, anomaly_labels
    
    def _difficulty_based_flip_assist17(self, s: torch.Tensor, q: torch.Tensor, 
                                       pid: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于ASSIST17问题难度的异常生成
        
        利用问题ID和问题类型ID来估算难度
        """
        s_flip = s.clone()
        labels = torch.zeros_like(s)
        
        valid_mask = (s >= 0)
        valid_indices = torch.where(valid_mask)[0]
        
        if len(valid_indices) < 5:
            return s_flip, labels
        
        # 估算问题难度
        difficulties = self._estimate_question_difficulty(q, pid)
        
        # 选择异常区间
        flip_len = random.randint(3, min(10, len(valid_indices) // 2))
        start_idx = random.randint(0, len(valid_indices) - flip_len)
        flip_indices = valid_indices[start_idx:start_idx + flip_len]
        
        for idx in flip_indices:
            q_id = q[idx].item()
            difficulty = difficulties[idx].item()
            
            # 基于难度生成异常
            if difficulty < 0.3:  # 简单题目
                if random.random() < 0.7:  # 70%概率生成"失误"异常
                    s_flip[idx] = 0  # 简单题答错
                    labels[idx] = 1
            elif difficulty > 0.7:  # 困难题目
                if random.random() < 0.6:  # 60%概率生成"猜测"异常
                    s_flip[idx] = 1  # 困难题答对
                    labels[idx] = 1
        
        return s_flip, labels
    
    def _skill_jump_anomaly(self, s: torch.Tensor, q: torch.Tensor,
                           pid: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        技能跳跃异常：学生在没有掌握前置技能的情况下掌握高级技能
        """
        s_flip = s.clone()
        labels = torch.zeros_like(s)
        
        if pid is None:
            return s_flip, labels
        
        valid_mask = (s >= 0)
        valid_indices = torch.where(valid_mask)[0]
        
        if len(valid_indices) < 10:
            return s_flip, labels
        
        # 分析技能序列
        skill_sequence = pid[valid_mask]
        unique_skills = torch.unique(skill_sequence)
        
        # 模拟技能跳跃：在某个高级技能上突然表现很好
        if len(unique_skills) > 3:
            # 选择一个"高级"技能（出现较晚的技能）
            skill_positions = {}
            for i, skill in enumerate(skill_sequence):
                if skill.item() not in skill_positions:
                    skill_positions[skill.item()] = i
            
            # 选择出现位置在后半段的技能作为"高级技能"
            advanced_skills = [skill for skill, pos in skill_positions.items() 
                             if pos > len(skill_sequence) // 2]
            
            if advanced_skills:
                target_skill = random.choice(advanced_skills)
                skill_positions_in_seq = (pid == target_skill) & valid_mask
                skill_indices = torch.where(skill_positions_in_seq)[0]
                
                # 在这个技能的前几次出现时制造"跳跃"异常
                if len(skill_indices) > 2:
                    jump_indices = skill_indices[:min(3, len(skill_indices))]
                    for idx in jump_indices:
                        if s[idx] == 0:  # 原本答错的，改为答对（技能跳跃）
                            s_flip[idx] = 1
                            labels[idx] = 1
        
        return s_flip, labels
    
    def _time_based_anomaly(self, s: torch.Tensor, q: torch.Tensor,
                           at: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于时间的异常：模拟疲劳效应或时间压力
        """
        s_flip = s.clone()
        labels = torch.zeros_like(s)
        
        if at is None:
            # 如果没有时间信息，使用序列位置模拟时间效应
            return self._simulate_fatigue_by_position(s, q)
        
        valid_mask = (s >= 0) & (at >= 0)
        valid_indices = torch.where(valid_mask)[0]
        
        if len(valid_indices) < 10:
            return s_flip, labels
        
        # 分析答题时间模式
        times = at[valid_mask].float()
        
        # 检测异常快速或异常慢速的答题
        median_time = torch.median(times)
        
        for idx in valid_indices:
            time_val = at[idx].float()
            
            # 异常快速答题（可能是随机猜测）
            if time_val < median_time * 0.3:
                if random.random() < 0.5:
                    s_flip[idx] = random.randint(0, 1)  # 随机答案
                    labels[idx] = 1
            
            # 异常慢速答题（可能是困惑或作弊）
            elif time_val > median_time * 3:
                if random.random() < 0.3:
                    # 慢速答题后可能答对（查资料）或答错（困惑）
                    s_flip[idx] = 1 - s_flip[idx]
                    labels[idx] = 1
        
        return s_flip, labels
    
    def _interaction_pattern_anomaly(self, s: torch.Tensor, q: torch.Tensor,
                                   it: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于交互类型的异常模式
        """
        s_flip = s.clone()
        labels = torch.zeros_like(s)
        
        if it is None:
            return s_flip, labels
        
        valid_mask = (s >= 0)
        valid_indices = torch.where(valid_mask)[0]
        
        if len(valid_indices) < 5:
            return s_flip, labels
        
        # 分析交互类型模式
        interaction_types = it[valid_mask]
        
        # 检测异常的交互模式（例如：某种交互类型的表现异常）
        unique_interactions = torch.unique(interaction_types)
        
        if len(unique_interactions) > 1:
            # 选择一种交互类型制造异常
            target_interaction = random.choice(unique_interactions.tolist())
            interaction_mask = (it == target_interaction) & valid_mask
            interaction_indices = torch.where(interaction_mask)[0]
            
            if len(interaction_indices) > 2:
                # 在这种交互类型下制造异常表现
                anomaly_count = min(3, len(interaction_indices))
                selected_indices = random.sample(interaction_indices.tolist(), anomaly_count)
                
                for idx in selected_indices:
                    s_flip[idx] = 1 - s_flip[idx]  # 翻转答案
                    labels[idx] = 1
        
        return s_flip, labels
    
    def _simulate_fatigue_by_position(self, s: torch.Tensor, q: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于序列位置模拟疲劳效应
        """
        s_flip = s.clone()
        labels = torch.zeros_like(s)
        
        valid_mask = (s >= 0)
        valid_indices = torch.where(valid_mask)[0]
        seq_len = len(valid_indices)
        
        if seq_len < 20:
            return s_flip, labels
        
        # 在序列后半段模拟疲劳效应
        fatigue_start = int(seq_len * 0.7)  # 从70%位置开始疲劳
        fatigue_indices = valid_indices[fatigue_start:]
        
        # 疲劳导致的错误率增加
        for i, idx in enumerate(fatigue_indices):
            fatigue_level = i / len(fatigue_indices)  # 疲劳程度递增
            error_prob = 0.2 + fatigue_level * 0.3  # 20%-50%的错误概率
            
            if random.random() < error_prob:
                if s[idx] == 1:  # 原本答对的改为答错
                    s_flip[idx] = 0
                    labels[idx] = 1
        
        return s_flip, labels
    
    def _estimate_question_difficulty(self, q: torch.Tensor, 
                                    pid: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        估算问题难度
        
        优先使用预计算的难度，否则基于问题ID和类型ID估算
        """
        difficulties = torch.ones_like(q, dtype=torch.float) * 0.5  # 默认中等难度
        
        for i, q_id in enumerate(q):
            q_id_item = q_id.item()
            
            # 使用预计算的问题难度
            if q_id_item in self.question_difficulty:
                difficulties[i] = self.question_difficulty[q_id_item]
            else:
                # 基于问题ID的简单估算
                # 假设问题ID越大，难度越高（这是一个简化假设）
                difficulties[i] = min(0.9, 0.3 + (q_id_item % 102) / 102 * 0.6)
        
        return difficulties
    
    def compute_dataset_statistics(self, data_loader) -> Dict:
        """
        计算数据集统计信息，用于改进异常生成
        
        Args:
            data_loader: ASSIST17数据加载器
            
        Returns:
            统计信息字典
        """
        question_stats = defaultdict(list)
        skill_stats = defaultdict(list)
        
        print("🔍 计算ASSIST17数据集统计信息...")
        
        for batch in data_loader:
            q = batch.get('q')
            s = batch.get('s') 
            pid = batch.get('pid')
            
            if q is None or s is None:
                continue
                
            batch_size, seq_len = s.shape
            
            for i in range(batch_size):
                valid_mask = s[i] >= 0
                valid_q = q[i][valid_mask]
                valid_s = s[i][valid_mask]
                valid_pid = pid[i][valid_mask] if pid is not None else None
                
                # 统计问题难度
                for j in range(len(valid_q)):
                    q_id = valid_q[j].item()
                    correct = valid_s[j].item()
                    question_stats[q_id].append(correct)
                    
                    if valid_pid is not None:
                        skill_id = valid_pid[j].item()
                        skill_stats[skill_id].append(correct)
        
        # 计算难度（错误率）
        question_difficulty = {}
        for q_id, results in question_stats.items():
            if len(results) >= 5:  # 至少5次作答
                question_difficulty[q_id] = 1 - np.mean(results)
        
        skill_difficulty = {}
        for skill_id, results in skill_stats.items():
            if len(results) >= 10:  # 至少10次作答
                skill_difficulty[skill_id] = 1 - np.mean(results)
        
        print(f"✓ 计算完成: {len(question_difficulty)}个问题, {len(skill_difficulty)}个技能")
        
        return {
            'question_difficulty': question_difficulty,
            'skill_difficulty': skill_difficulty,
            'question_stats': dict(question_stats),
            'skill_stats': dict(skill_stats)
        }
