# ASSIST17数据集优化配置
# 基于ASSIST17数据集特点的专门优化配置

# 基本配置
data_dir: "data"
device: "auto"
with_pid: true  # ASSIST17有丰富的问题ID信息

# 数据参数 - 针对ASSIST17优化
batch_size: 32  # ASSIST17数据量大，使用中等批次
test_batch_size: 64

# 基线模型参数 - 针对ASSIST17复杂性
d_model: 128
n_heads: 8
n_know: 32  # ASSIST17有较多知识点
n_layers: 3
dropout: 0.2
lambda_cl: 0.1
proj: true
hard_neg: false
window: 1

# 异常检测器参数 - ASSIST17特定优化
detector_d_model: 128
detector_n_heads: 8
detector_n_layers: 2
detector_dropout: 0.1
window_size: 10
anomaly_ratio: 0.15  # ASSIST17中异常行为相对较多
optimize_for: "f1_score"

# 训练参数 - 充分训练
kt_epochs: 100
detector_epochs: 50
learning_rate: 1e-3
detector_lr: 1e-3
patience: 15
detector_patience: 15
use_cl: true

# 异常感知参数 - 平衡配置
anomaly_weight: 0.08  # 从较小权重开始
use_enhanced_anomaly_aware: true
use_aa_curriculum: true
use_aa_game_theory: true

# 训练策略 - 增强策略适合ASSIST17
training_strategy: "enhanced"
use_curriculum: true

# 控制参数
skip_baseline: false
skip_detector: false
skip_anomaly_training: false

# ASSIST17特定建议
# 1. 使用--with_pid参数，ASSIST17的问题ID信息很有价值
# 2. 异常比例可以设置为0.15，ASSIST17中学习行为变化较多
# 3. 建议使用enhanced策略，能更好处理ASSIST17的复杂性
# 4. 如果计算资源充足，可以增加n_know到64
