"""
课程学习策略模块

提供课程学习调度器和相关工具函数，用于实现渐进式训练
"""

from typing import Dict, Any, List, Tuple, Optional
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path


class CurriculumScheduler:
    """课程学习调度器"""
    
    def __init__(self, total_epochs: int = 50):
        """
        初始化课程学习调度器
        
        Args:
            total_epochs: 总训练轮数
        """
        self.total_epochs = total_epochs
        self.stages = [
            (0.2, {
                'name': 'warmup',
                'description': '热身阶段 - 简单异常模式',
                'anomaly_ratio': 0.05,
                'difficulty': 'easy',
                'max_seq_len': 50,
                'allowed_types': ['random'],
                'focal_gamma': 1.0,
                'loss_weights': {'bce': 1.0, 'focal': 0.0, 'contrastive': 0.0}
            }),
            (0.5, {
                'name': 'intermediate',
                'description': '中级阶段 - 引入模式异常',
                'anomaly_ratio': 0.15,
                'difficulty': 'medium',
                'max_seq_len': 100,
                'allowed_types': ['random', 'pattern'],
                'focal_gamma': 1.5,
                'loss_weights': {'bce': 0.7, 'focal': 0.3, 'contrastive': 0.0}
            }),
            (0.8, {
                'name': 'advanced',
                'description': '高级阶段 - 全部异常类型',
                'anomaly_ratio': 0.25,
                'difficulty': 'hard',
                'max_seq_len': 200,
                'allowed_types': ['random', 'pattern', 'difficulty'],
                'focal_gamma': 2.0,
                'loss_weights': {'bce': 0.3, 'focal': 0.5, 'contrastive': 0.2}
            }),
            (1.0, {
                'name': 'expert',
                'description': '专家阶段 - 最大难度',
                'anomaly_ratio': 0.3,
                'difficulty': 'expert',
                'max_seq_len': -1,  # 无限制
                'allowed_types': 'all',
                'focal_gamma': 2.5,
                'loss_weights': {'bce': 0.0, 'focal': 0.7, 'contrastive': 0.3}
            })
        ]
        
        # 记录训练历史
        self.history = {
            'epochs': [],
            'stages': [],
            'anomaly_ratios': [],
            'difficulties': []
        }
    
    def get_stage_params(self, epoch: int) -> Dict[str, Any]:
        """
        获取当前训练阶段的参数
        
        Args:
            epoch: 当前训练轮数
            
        Returns:
            包含当前阶段参数的字典
        """
        progress = epoch / self.total_epochs
        for threshold, params in self.stages:
            if progress <= threshold:
                # 记录历史
                self.history['epochs'].append(epoch)
                self.history['stages'].append(params['name'])
                self.history['anomaly_ratios'].append(params['anomaly_ratio'])
                self.history['difficulties'].append(params['difficulty'])
                return params
        
        # 默认返回最后一个阶段
        last_params = self.stages[-1][1]
        self.history['epochs'].append(epoch)
        self.history['stages'].append(last_params['name'])
        self.history['anomaly_ratios'].append(last_params['anomaly_ratio'])
        self.history['difficulties'].append(last_params['difficulty'])
        return last_params
    
    def get_transition_epochs(self) -> List[int]:
        """获取阶段转换的epoch列表"""
        return [int(threshold * self.total_epochs) for threshold, _ in self.stages]
    
    def plot_curriculum(self, save_path: Optional[str] = None):
        """
        绘制课程学习进度图
        
        Args:
            save_path: 保存路径，如果为None则显示图像
        """
        if not self.history['epochs']:
            print("没有训练历史记录，无法绘制课程图")
            return
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
        
        # 绘制异常比例
        ax1.plot(self.history['epochs'], self.history['anomaly_ratios'], 'b-', marker='o')
        ax1.set_ylabel('异常比例')
        ax1.set_title('课程学习进度')
        ax1.grid(True)
        
        # 绘制阶段变化
        stages = list(set(self.history['stages']))
        stage_values = [stages.index(s) for s in self.history['stages']]
        ax2.step(self.history['epochs'], stage_values, 'r-', where='post')
        ax2.set_yticks(range(len(stages)))
        ax2.set_yticklabels(stages)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('训练阶段')
        ax2.grid(True)
        
        # 添加阶段转换线
        for epoch in self.get_transition_epochs():
            if epoch > 0:  # 跳过第一个阶段的开始
                ax1.axvline(x=epoch, color='gray', linestyle='--', alpha=0.7)
                ax2.axvline(x=epoch, color='gray', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            print(f"课程学习进度图已保存至: {save_path}")
        else:
            plt.show()


class DifficultyAdjuster:
    """难度调整器"""
    
    def __init__(self, base_difficulty: float = 0.5):
        """
        初始化难度调整器
        
        Args:
            base_difficulty: 基础难度值 (0-1)
        """
        self.base_difficulty = base_difficulty
        self.difficulty_history = []
    
    def get_difficulty_params(self, difficulty_level: str) -> Dict[str, Any]:
        """
        根据难度级别获取参数
        
        Args:
            difficulty_level: 难度级别 ('easy', 'medium', 'hard', 'expert')
            
        Returns:
            难度参数字典
        """
        difficulty_params = {
            'easy': {
                'max_consecutive': 2,
                'pattern_size': 2,
                'burst_probability': 0.1,
                'max_pattern_repeat': 2
            },
            'medium': {
                'max_consecutive': 3,
                'pattern_size': 3,
                'burst_probability': 0.2,
                'max_pattern_repeat': 3
            },
            'hard': {
                'max_consecutive': 5,
                'pattern_size': 4,
                'burst_probability': 0.3,
                'max_pattern_repeat': 4
            },
            'expert': {
                'max_consecutive': 8,
                'pattern_size': 5,
                'burst_probability': 0.4,
                'max_pattern_repeat': 5
            }
        }
        
        if difficulty_level not in difficulty_params:
            print(f"警告: 未知难度级别 '{difficulty_level}'，使用 'medium'")
            difficulty_level = 'medium'
        
        return difficulty_params[difficulty_level]
    
    def adjust_for_performance(self, 
                              current_metrics: Dict[str, float], 
                              target_metrics: Dict[str, float],
                              current_difficulty: str) -> str:
        """
        根据性能指标动态调整难度
        
        Args:
            current_metrics: 当前性能指标
            target_metrics: 目标性能指标
            current_difficulty: 当前难度级别
            
        Returns:
            调整后的难度级别
        """
        # 计算性能差距
        f1_gap = current_metrics.get('f1_score', 0) - target_metrics.get('f1_score', 0.7)
        
        difficulty_levels = ['easy', 'medium', 'hard', 'expert']
        current_idx = difficulty_levels.index(current_difficulty)
        
        # 根据性能差距调整难度
        if f1_gap > 0.2:  # 性能远超目标
            new_idx = min(current_idx + 1, len(difficulty_levels) - 1)
        elif f1_gap < -0.2:  # 性能远低于目标
            new_idx = max(current_idx - 1, 0)
        else:
            new_idx = current_idx
        
        return difficulty_levels[new_idx]


def create_curriculum_config(save_path: str, total_epochs: int = 50):
    """
    创建课程学习配置文件
    
    Args:
        save_path: 配置文件保存路径
        total_epochs: 总训练轮数
    """
    import yaml
    
    scheduler = CurriculumScheduler(total_epochs=total_epochs)
    
    config = {
        'curriculum_learning': {
            'enabled': True,
            'total_epochs': total_epochs,
            'stages': []
        }
    }
    
    # 添加各阶段配置
    for threshold, params in scheduler.stages:
        stage_epochs = int(threshold * total_epochs)
        config['curriculum_learning']['stages'].append({
            'name': params['name'],
            'end_epoch': stage_epochs,
            'params': {
                'anomaly_ratio': params['anomaly_ratio'],
                'difficulty': params['difficulty'],
                'allowed_types': params['allowed_types'],
                'focal_gamma': params.get('focal_gamma', 2.0),
                'loss_weights': params.get('loss_weights', {})
            }
        })
    
    # 保存配置 (使用UTF-8编码)
    with open(save_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"课程学习配置已保存至: {save_path}")
    return config