# Anomaly Generator Implementation Specification

## Overview

This document provides the technical implementation specification for the scientifically-grounded anomaly generator framework described in our research paper. The implementation follows academic best practices and ensures reproducibility across research environments.

## 1. Architecture Design

### 1.1 Core Components

```
AnomalyGeneratorFramework/
├── core/
│   ├── base_generator.py          # Abstract base class
│   ├── cognitive_models.py        # Cognitive science models
│   └── validation_engine.py       # Quality validation
├── strategies/
│   ├── cognitive_load.py          # Cognitive load anomalies
│   ├── metacognitive.py           # Metacognitive anomalies
│   ├── motivational.py            # Motivational anomalies
│   └── external_interference.py   # External anomalies
├── quality/
│   ├── consistency_validator.py   # Cross-stage consistency
│   ├── statistical_metrics.py     # Statistical quality measures
│   └── cognitive_validator.py     # Pedagogical validity
└── utils/
    ├── config_manager.py          # Configuration management
    ├── reproducibility.py         # Reproducibility utilities
    └── visualization.py           # Analysis and debugging tools
```

### 1.2 Design Principles

1. **Scientific Rigor**: All algorithms based on established cognitive science theories
2. **Reproducibility**: Deterministic generation with seed control
3. **Modularity**: Pluggable components for different anomaly types
4. **Validation**: Comprehensive quality control at multiple levels
5. **Scalability**: Efficient implementation for large-scale experiments

## 2. Core Implementation

### 2.1 Base Generator Interface

```python
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Optional, List
import torch
import numpy as np

class BaseAnomalyGenerator(ABC):
    """
    Abstract base class for all anomaly generators.
    Ensures consistent interface across different anomaly types.
    """
    
    def __init__(self, config: Dict, random_seed: Optional[int] = None):
        """
        Initialize the anomaly generator.
        
        Args:
            config: Configuration dictionary with generator parameters
            random_seed: Random seed for reproducibility
        """
        self.config = config
        self.random_seed = random_seed
        self._setup_reproducibility()
        
    def _setup_reproducibility(self):
        """Setup reproducible random number generation."""
        if self.random_seed is not None:
            torch.manual_seed(self.random_seed)
            np.random.seed(self.random_seed)
            
    @abstractmethod
    def generate_anomalies(self, 
                          sequence: torch.Tensor,
                          metadata: Dict,
                          **kwargs) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate anomalies in the given sequence.
        
        Args:
            sequence: Original response sequence [batch_size, seq_len]
            metadata: Additional information (difficulties, prerequisites, etc.)
            **kwargs: Strategy-specific parameters
            
        Returns:
            anomaly_sequence: Modified sequence with anomalies
            anomaly_labels: Binary labels indicating anomaly positions
        """
        pass
        
    @abstractmethod
    def validate_output(self, 
                       original: torch.Tensor,
                       anomaly: torch.Tensor,
                       labels: torch.Tensor,
                       metadata: Dict) -> Dict[str, float]:
        """
        Validate the quality of generated anomalies.
        
        Returns:
            validation_metrics: Dictionary of quality scores
        """
        pass
        
    def get_theoretical_foundation(self) -> Dict[str, str]:
        """Return the theoretical basis for this anomaly type."""
        return {
            'theory': self.__class__.__doc__,
            'references': getattr(self, 'REFERENCES', []),
            'cognitive_basis': getattr(self, 'COGNITIVE_BASIS', 'Not specified')
        }
```

### 2.2 Cognitive Load Anomaly Generator

```python
class CognitiveLoadAnomalyGenerator(BaseAnomalyGenerator):
    """
    Generates anomalies based on Cognitive Load Theory (Sweller, 1988).
    
    Theoretical Foundation:
    - Intrinsic load: Task complexity
    - Extraneous load: Poor instructional design
    - Germane load: Schema construction
    
    Anomaly Manifestations:
    - Performance degradation under high cognitive load
    - Fatigue effects in extended sessions
    - Overload-induced random responses
    """
    
    REFERENCES = [
        "Sweller, J. (1988). Cognitive load during problem solving",
        "Paas, F., et al. (2003). Cognitive load measurement as a means to advance cognitive load theory"
    ]
    
    COGNITIVE_BASIS = "Cognitive Load Theory - Working memory limitations"
    
    def __init__(self, config: Dict, random_seed: Optional[int] = None):
        super().__init__(config, random_seed)
        
        # Cognitive load parameters
        self.intrinsic_load_weight = config.get('intrinsic_load_weight', 0.6)
        self.extraneous_load_weight = config.get('extraneous_load_weight', 0.3)
        self.germane_load_weight = config.get('germane_load_weight', 0.1)
        self.fatigue_threshold = config.get('fatigue_threshold', 0.7)
        self.overload_threshold = config.get('overload_threshold', 0.9)
        
    def generate_anomalies(self, 
                          sequence: torch.Tensor,
                          metadata: Dict,
                          **kwargs) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generate cognitive load-based anomalies."""
        
        batch_size, seq_len = sequence.shape
        anomaly_sequence = sequence.clone()
        anomaly_labels = torch.zeros_like(sequence)
        
        # Extract metadata
        difficulties = metadata.get('difficulties', torch.ones_like(sequence) * 0.5)
        session_lengths = metadata.get('session_lengths', torch.full((batch_size,), seq_len))
        
        for batch_idx in range(batch_size):
            # Calculate cognitive load components
            intrinsic_load = self._calculate_intrinsic_load(
                difficulties[batch_idx], sequence[batch_idx]
            )
            extraneous_load = self._calculate_extraneous_load(
                sequence[batch_idx], session_lengths[batch_idx]
            )
            germane_load = self._calculate_germane_load(
                sequence[batch_idx], difficulties[batch_idx]
            )
            
            # Total cognitive load
            total_load = (self.intrinsic_load_weight * intrinsic_load + 
                         self.extraneous_load_weight * extraneous_load +
                         self.germane_load_weight * germane_load)
            
            # Generate anomalies based on load
            anomaly_sequence[batch_idx], anomaly_labels[batch_idx] = \
                self._apply_cognitive_load_effects(
                    sequence[batch_idx], total_load
                )
                
        return anomaly_sequence, anomaly_labels
    
    def _calculate_intrinsic_load(self, difficulties: torch.Tensor, 
                                 responses: torch.Tensor) -> torch.Tensor:
        """Calculate intrinsic cognitive load based on task difficulty."""
        # Intrinsic load increases with difficulty and decreases with success
        success_factor = responses.float()
        load = difficulties * (2.0 - success_factor)  # Higher load for failures
        
        # Cumulative effect - load accumulates over time
        cumulative_load = torch.cumsum(load, dim=0) / torch.arange(1, len(load) + 1)
        
        return cumulative_load
    
    def _calculate_extraneous_load(self, responses: torch.Tensor, 
                                  session_length: torch.Tensor) -> torch.Tensor:
        """Calculate extraneous cognitive load from session factors."""
        seq_len = len(responses)
        
        # Fatigue increases linearly with session progress
        fatigue_factor = torch.linspace(0, 1, seq_len)
        
        # Distraction effects (modeled as random spikes)
        distraction_factor = torch.rand(seq_len) * 0.3
        
        # Session length penalty
        length_penalty = min(1.0, session_length.float() / 1000.0)
        
        extraneous_load = (fatigue_factor + distraction_factor) * length_penalty
        
        return extraneous_load
    
    def _calculate_germane_load(self, responses: torch.Tensor,
                               difficulties: torch.Tensor) -> torch.Tensor:
        """Calculate germane cognitive load from learning processes."""
        seq_len = len(responses)
        
        # Learning effort increases with difficulty but decreases with mastery
        mastery_level = torch.cumsum(responses.float(), dim=0) / torch.arange(1, seq_len + 1)
        learning_effort = difficulties * (1.0 - mastery_level)
        
        # Schema construction load
        schema_load = torch.zeros(seq_len)
        for i in range(1, seq_len):
            # Higher load when encountering new difficulty levels
            difficulty_change = abs(difficulties[i] - difficulties[i-1])
            schema_load[i] = difficulty_change * 0.5
            
        germane_load = learning_effort + schema_load
        
        return germane_load
    
    def _apply_cognitive_load_effects(self, sequence: torch.Tensor,
                                     cognitive_load: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Apply cognitive load effects to generate anomalies."""
        anomaly_sequence = sequence.clone()
        anomaly_labels = torch.zeros_like(sequence)
        
        # Fatigue effects
        fatigue_mask = cognitive_load > self.fatigue_threshold
        fatigue_positions = torch.where(fatigue_mask)[0]
        
        for pos in fatigue_positions:
            # Probability of error increases with cognitive load
            error_probability = min(0.8, (cognitive_load[pos] - self.fatigue_threshold) * 2)
            
            if torch.rand(1) < error_probability:
                # Fatigue typically causes correct -> incorrect transitions
                if sequence[pos] == 1:
                    anomaly_sequence[pos] = 0
                    anomaly_labels[pos] = 1
        
        # Overload effects
        overload_mask = cognitive_load > self.overload_threshold
        overload_positions = torch.where(overload_mask)[0]
        
        for pos in overload_positions:
            # Overload causes random responses
            if torch.rand(1) < 0.6:  # 60% chance of random response
                anomaly_sequence[pos] = torch.randint(0, 2, (1,)).item()
                anomaly_labels[pos] = 1
                
        return anomaly_sequence, anomaly_labels
    
    def validate_output(self, original: torch.Tensor, anomaly: torch.Tensor,
                       labels: torch.Tensor, metadata: Dict) -> Dict[str, float]:
        """Validate cognitive load anomaly generation."""
        metrics = {}
        
        # 1. Fatigue progression validation
        metrics['fatigue_progression'] = self._validate_fatigue_progression(
            original, anomaly, labels
        )
        
        # 2. Load-performance correlation
        metrics['load_correlation'] = self._validate_load_correlation(
            anomaly, labels, metadata.get('difficulties')
        )
        
        # 3. Temporal consistency
        metrics['temporal_consistency'] = self._validate_temporal_consistency(
            labels
        )
        
        return metrics
    
    def _validate_fatigue_progression(self, original: torch.Tensor,
                                    anomaly: torch.Tensor,
                                    labels: torch.Tensor) -> float:
        """Validate that fatigue effects increase over time."""
        seq_len = original.shape[-1]
        
        # Calculate anomaly density in different time windows
        window_size = seq_len // 4
        densities = []
        
        for i in range(0, seq_len - window_size, window_size):
            window_labels = labels[..., i:i+window_size]
            density = window_labels.float().mean().item()
            densities.append(density)
        
        # Check if anomaly density increases over time (fatigue effect)
        if len(densities) > 1:
            correlation = np.corrcoef(range(len(densities)), densities)[0, 1]
            return max(0, correlation)  # Positive correlation indicates proper fatigue
        
        return 1.0
```

## 3. Quality Assurance Framework

### 3.1 Multi-Level Validation

```python
class ComprehensiveQualityValidator:
    """
    Multi-level validation framework for anomaly generation quality.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.cognitive_validator = CognitiveConsistencyValidator(config)
        self.statistical_validator = StatisticalQualityValidator(config)
        self.pedagogical_validator = PedagogicalValidityValidator(config)
        
    def validate_generation_batch(self, 
                                 original_sequences: torch.Tensor,
                                 anomaly_sequences: torch.Tensor,
                                 anomaly_labels: torch.Tensor,
                                 metadata: Dict) -> Dict[str, float]:
        """
        Comprehensive validation of a batch of generated anomalies.
        """
        validation_results = {}
        
        # Level 1: Cognitive consistency
        cognitive_metrics = self.cognitive_validator.validate_batch(
            original_sequences, anomaly_sequences, anomaly_labels, metadata
        )
        validation_results.update({f'cognitive_{k}': v for k, v in cognitive_metrics.items()})
        
        # Level 2: Statistical quality
        statistical_metrics = self.statistical_validator.validate_batch(
            anomaly_labels, metadata
        )
        validation_results.update({f'statistical_{k}': v for k, v in statistical_metrics.items()})
        
        # Level 3: Pedagogical validity
        pedagogical_metrics = self.pedagogical_validator.validate_batch(
            original_sequences, anomaly_sequences, metadata
        )
        validation_results.update({f'pedagogical_{k}': v for k, v in pedagogical_metrics.items()})
        
        # Overall quality score
        validation_results['overall_quality'] = self._calculate_overall_quality(
            validation_results
        )
        
        return validation_results
    
    def _calculate_overall_quality(self, metrics: Dict[str, float]) -> float:
        """Calculate weighted overall quality score."""
        weights = self.config.get('quality_weights', {
            'cognitive': 0.4,
            'statistical': 0.3,
            'pedagogical': 0.3
        })
        
        cognitive_score = np.mean([v for k, v in metrics.items() if k.startswith('cognitive_')])
        statistical_score = np.mean([v for k, v in metrics.items() if k.startswith('statistical_')])
        pedagogical_score = np.mean([v for k, v in metrics.items() if k.startswith('pedagogical_')])
        
        overall_quality = (weights['cognitive'] * cognitive_score +
                          weights['statistical'] * statistical_score +
                          weights['pedagogical'] * pedagogical_score)
        
        return overall_quality
```

## 4. Configuration Management

### 4.1 Hierarchical Configuration System

```yaml
# Scientific Anomaly Generator Configuration
anomaly_generation:
  # Global settings
  global:
    random_seed: 42
    reproducibility_level: "strict"  # strict, moderate, relaxed
    validation_level: "comprehensive"  # basic, standard, comprehensive
    
  # Theoretical foundations
  cognitive_theories:
    cognitive_load_theory:
      enabled: true
      parameters:
        intrinsic_weight: 0.6
        extraneous_weight: 0.3
        germane_weight: 0.1
        fatigue_threshold: 0.7
        
    metacognitive_theory:
      enabled: true
      parameters:
        overconfidence_factor: 0.3
        underconfidence_factor: 0.2
        strategy_error_rate: 0.15
        
  # Quality control
  validation:
    cognitive_consistency:
      max_difficulty_jump: 0.3
      min_learning_rate: -0.1
      prerequisite_threshold: 0.5
      
    statistical_quality:
      target_distribution: "beta"
      distribution_params:
        alpha: 2.0
        beta: 8.0
      kl_divergence_threshold: 0.1
      
    pedagogical_validity:
      expert_agreement_threshold: 0.8
      face_validity_threshold: 0.75
      
  # Research reproducibility
  reproducibility:
    version_control: true
    parameter_logging: true
    result_archiving: true
    cross_platform_consistency: true
```

## 5. Usage Examples

### 5.1 Basic Usage

```python
from anomaly_generator import ScientificAnomalyGenerator

# Initialize generator with configuration
config = load_config('scientific_anomaly_config.yaml')
generator = ScientificAnomalyGenerator(config, random_seed=42)

# Generate anomalies
anomaly_sequences, anomaly_labels = generator.generate_anomalies(
    sequences=student_responses,
    metadata={
        'difficulties': item_difficulties,
        'prerequisites': prerequisite_graph,
        'session_info': session_metadata
    },
    anomaly_ratio=0.15
)

# Validate quality
quality_metrics = generator.validate_quality(
    original=student_responses,
    anomaly=anomaly_sequences,
    labels=anomaly_labels
)

print(f"Overall quality score: {quality_metrics['overall_quality']:.3f}")
```

### 5.2 Research Pipeline Integration

```python
# Integration with knowledge tracing pipeline
class AnomalyAwareKnowledgeTracingPipeline:
    def __init__(self, config):
        self.anomaly_generator = ScientificAnomalyGenerator(
            config['anomaly_generation'], 
            random_seed=config['global']['random_seed']
        )
        
    def train_detector(self, train_data, val_data):
        """Train anomaly detector with scientifically generated anomalies."""
        
        # Generate training anomalies
        train_anomalies, train_labels = self.anomaly_generator.generate_anomalies(
            sequences=train_data.responses,
            metadata=train_data.metadata,
            anomaly_ratio=0.15
        )
        
        # Validate generation quality
        quality_metrics = self.anomaly_generator.validate_quality(
            original=train_data.responses,
            anomaly=train_anomalies,
            labels=train_labels
        )
        
        # Log quality metrics for research reproducibility
        self._log_quality_metrics(quality_metrics)
        
        # Train detector
        detector = self._train_detector_model(train_anomalies, train_labels)
        
        return detector
```

This implementation specification provides a complete framework for implementing the scientifically-grounded anomaly generator described in the research paper, ensuring both theoretical rigor and practical utility.
