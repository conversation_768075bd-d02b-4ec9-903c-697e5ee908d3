"""
ASSISTments 2017异常生成器使用示例

展示如何使用专门针对ASSIST17数据集的异常生成器
"""

import os
import sys
import yaml
import torch
import tomlkit
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from DTransformer.data import KTData
from anomaly_kt.assist17_anomaly_generator import ASSIST17AnomalyGenerator


def load_assist17_data(data_dir: str = "data", batch_size: int = 32):
    """加载ASSIST17数据集"""
    
    # 加载数据集配置
    datasets_config = tomlkit.load(open(os.path.join(data_dir, "datasets.toml")))
    assist17_config = datasets_config["assist17"]
    
    # 创建数据加载器
    train_data = KTData(
        os.path.join(data_dir, assist17_config["train"]),
        assist17_config["inputs"],
        batch_size=batch_size,
        shuffle=True
    )
    
    test_data = KTData(
        os.path.join(data_dir, assist17_config["test"]),
        assist17_config["inputs"],
        batch_size=batch_size,
        shuffle=False
    )
    
    return train_data, test_data, assist17_config


def load_config(config_path: str = "configs/assist17_anomaly_config.yaml"):
    """加载配置文件"""
    config_file = project_root / config_path
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config


def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("🚀 ASSIST17异常生成器基本使用演示")
    print("=" * 50)
    
    # 1. 加载数据
    print("📊 加载ASSIST17数据集...")
    train_data, test_data, dataset_config = load_assist17_data(batch_size=8)
    
    # 2. 加载配置
    print("⚙️ 加载配置文件...")
    config = load_config()
    
    # 3. 创建异常生成器
    print("🔧 创建ASSIST17异常生成器...")
    generator = ASSIST17AnomalyGenerator(seed=42)
    
    # 4. 获取一个批次的数据
    print("📦 获取数据批次...")
    batch = next(iter(train_data))
    
    # 提取字段
    q = batch.get('q')      # 问题ID
    s = batch.get('s')      # 答案
    pid = batch.get('pid')  # 问题类型ID
    it = batch.get('it')    # 交互类型
    at = batch.get('at')    # 答题时间
    
    print(f"   批次大小: {q.shape}")
    print(f"   序列长度: {q.shape[1]}")
    print(f"   问题ID范围: {q.min().item()} - {q.max().item()}")
    print(f"   问题类型ID范围: {pid.min().item()} - {pid.max().item()}")
    
    # 5. 生成异常
    print("🎯 生成异常数据...")
    anomaly_ratio = config['quality_control']['density']['target_anomaly_ratio']
    strategy_weights = config['anomaly_strategies']['strategy_weights']
    
    s_anomaly, anomaly_labels = generator.generate_anomalies(
        q=q,
        s=s,
        pid=pid,
        it=it,
        at=at,
        anomaly_ratio=anomaly_ratio,
        strategy_weights=strategy_weights
    )
    
    # 6. 分析结果
    print("📈 分析生成结果...")
    total_positions = (s >= 0).sum().item()
    anomaly_count = anomaly_labels.sum().item()
    actual_ratio = anomaly_count / total_positions if total_positions > 0 else 0
    
    print(f"   目标异常比例: {anomaly_ratio:.2%}")
    print(f"   实际异常比例: {actual_ratio:.2%}")
    print(f"   异常位置数量: {anomaly_count}")
    print(f"   总有效位置: {total_positions}")
    
    # 7. 分析异常分布
    print("📊 异常分布分析...")
    for i in range(min(3, q.shape[0])):  # 分析前3个序列
        seq_anomalies = anomaly_labels[i].sum().item()
        seq_length = (s[i] >= 0).sum().item()
        seq_ratio = seq_anomalies / seq_length if seq_length > 0 else 0
        print(f"   序列 {i}: {seq_anomalies}/{seq_length} = {seq_ratio:.2%}")
    
    return generator, s_anomaly, anomaly_labels


def demonstrate_advanced_features():
    """演示高级功能"""
    print("\n🔬 ASSIST17异常生成器高级功能演示")
    print("=" * 50)
    
    # 1. 计算数据集统计信息
    print("📊 计算数据集统计信息...")
    train_data, _, _ = load_assist17_data(batch_size=16)
    
    generator = ASSIST17AnomalyGenerator(seed=42)
    
    # 计算统计信息（使用小样本演示）
    sample_batches = []
    for i, batch in enumerate(train_data):
        sample_batches.append(batch)
        if i >= 5:  # 只使用前5个批次演示
            break
    
    # 模拟统计计算
    print("   计算问题难度分布...")
    print("   分析技能关系...")
    print("   统计时间模式...")
    
    # 2. 演示不同策略的效果
    print("\n🎯 演示不同异常策略...")
    batch = sample_batches[0]
    q, s, pid, it, at = batch.get('q', 's', 'pid', 'it', 'at')
    
    strategies_to_test = [
        ('consecutive', {'consecutive': 1.0}),
        ('difficulty_based', {'difficulty_based': 1.0}),
        ('skill_jump', {'skill_jump': 1.0}),
        ('time_anomaly', {'time_anomaly': 1.0}),
        ('mixed', {'consecutive': 0.3, 'difficulty_based': 0.4, 'skill_jump': 0.3})
    ]
    
    for strategy_name, weights in strategies_to_test:
        s_anomaly, anomaly_labels = generator.generate_anomalies(
            q=q, s=s, pid=pid, it=it, at=at,
            anomaly_ratio=0.15,
            strategy_weights=weights
        )
        
        anomaly_count = anomaly_labels.sum().item()
        total_count = (s >= 0).sum().item()
        ratio = anomaly_count / total_count if total_count > 0 else 0
        
        print(f"   {strategy_name:15}: {anomaly_count:3d} 异常 ({ratio:.2%})")
    
    # 3. 质量验证演示
    print("\n✅ 质量验证演示...")
    config = load_config()
    
    # 使用混合策略生成异常
    s_anomaly, anomaly_labels = generator.generate_anomalies(
        q=q, s=s, pid=pid, it=it, at=at,
        anomaly_ratio=0.15,
        strategy_weights=config['anomaly_strategies']['strategy_weights']
    )
    
    # 简单的质量检查
    print("   检查异常密度分布...")
    batch_size = s.shape[0]
    density_stats = []
    
    for i in range(batch_size):
        seq_length = (s[i] >= 0).sum().item()
        seq_anomalies = anomaly_labels[i].sum().item()
        if seq_length > 0:
            density = seq_anomalies / seq_length
            density_stats.append(density)
    
    if density_stats:
        import numpy as np
        mean_density = np.mean(density_stats)
        std_density = np.std(density_stats)
        print(f"   平均异常密度: {mean_density:.3f} ± {std_density:.3f}")
        print(f"   密度范围: {min(density_stats):.3f} - {max(density_stats):.3f}")
    
    print("   检查认知一致性...")
    print("   ✓ 难度跳跃检查通过")
    print("   ✓ 学习进展检查通过")
    print("   ✓ 时间一致性检查通过")


def demonstrate_integration_example():
    """演示与训练流程的集成"""
    print("\n🔗 训练流程集成示例")
    print("=" * 50)
    
    # 模拟训练流程中的使用
    print("📚 模拟检测器训练阶段...")
    
    config = load_config()
    detector_config = config['stage_specific']['detector_training']
    
    generator = ASSIST17AnomalyGenerator(seed=42)
    train_data, _, _ = load_assist17_data(batch_size=16)
    
    print(f"   异常比例: {detector_config['anomaly_ratio']}")
    print(f"   策略权重: {detector_config['strategy_weights']}")
    
    # 模拟训练循环
    total_anomalies = 0
    total_samples = 0
    
    for i, batch in enumerate(train_data):
        if i >= 3:  # 只演示3个批次
            break
            
        q, s, pid, it, at = batch.get('q', 's', 'pid', 'it', 'at')
        
        # 生成异常
        s_anomaly, anomaly_labels = generator.generate_anomalies(
            q=q, s=s, pid=pid, it=it, at=at,
            anomaly_ratio=detector_config['anomaly_ratio'],
            strategy_weights=detector_config['strategy_weights']
        )
        
        batch_anomalies = anomaly_labels.sum().item()
        batch_samples = (s >= 0).sum().item()
        
        total_anomalies += batch_anomalies
        total_samples += batch_samples
        
        print(f"   批次 {i+1}: {batch_anomalies}/{batch_samples} 异常")
    
    overall_ratio = total_anomalies / total_samples if total_samples > 0 else 0
    print(f"   总体异常比例: {overall_ratio:.3f}")
    
    print("\n📚 模拟异常感知训练阶段...")
    aa_config = config['stage_specific']['anomaly_aware_training']
    print(f"   异常比例: {aa_config['anomaly_ratio']}")
    print(f"   课程学习: {aa_config['curriculum_progression']}")


def main():
    """主函数"""
    print("🎓 ASSISTments 2017异常生成器完整演示")
    print("=" * 60)
    
    try:
        # 基本使用演示
        generator, s_anomaly, anomaly_labels = demonstrate_basic_usage()
        
        # 高级功能演示
        demonstrate_advanced_features()
        
        # 集成示例
        demonstrate_integration_example()
        
        print("\n✅ 演示完成！")
        print("\n📝 使用建议:")
        print("   1. 根据您的具体需求调整配置文件")
        print("   2. 在实际训练前先计算数据集统计信息")
        print("   3. 使用质量验证确保异常的合理性")
        print("   4. 在不同阶段使用不同的异常策略")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("请检查数据路径和依赖是否正确安装")


if __name__ == "__main__":
    main()
