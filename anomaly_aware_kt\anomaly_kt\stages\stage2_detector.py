"""
第2阶段：异常检测器训练

训练专门的异常检测器，用于识别学习者的异常答题行为。
支持多种训练策略：Basic, Enhanced, Aggressive
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Any
from DTransformer.data import KTData
from ..detector import CausalAnomalyDetector
from ..unified_trainer import UnifiedAnomalyTrainer


class DetectorTrainingStage:
    """第2阶段：异常检测器训练"""
    
    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化异常检测器训练阶段
        
        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir
        self.save_dir = os.path.join(output_dir, 'detector')
        os.makedirs(self.save_dir, exist_ok=True)
        
    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData) -> str:
        """
        执行异常检测器训练
        
        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据
            
        Returns:
            str: 保存的检测器模型路径
        """
        print("\n" + "="*60)
        print("STAGE 2: Training Anomaly Detector")
        print("="*60)
        print("🎯 目标: 训练高质量的异常检测器")
        print("🔍 功能: 识别学习者的异常答题行为")
        
        # 创建异常检测器
        detector = self._create_detector(dataset_config)
        print(f"🧠 检测器参数: {sum(p.numel() for p in detector.parameters()):,}")
        
        # 显示训练策略信息
        self._display_strategy_info()
        
        # 创建训练器
        trainer = self._create_trainer(detector)
        
        # 执行训练
        detector_metrics = self._train_detector(trainer, train_data, val_data)
        
        # 保存结果
        model_path = self._save_results(detector_metrics)
        
        print(f"\n✅ Stage 2 完成!")
        print(f"📈 最佳性能: {detector_metrics.get('best_score', 'N/A')}")
        print(f"💾 检测器保存至: {model_path}")
        
        return model_path
        
    def _create_detector(self, dataset_config: Dict) -> nn.Module:
        """创建异常检测器模型"""
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=getattr(self.args, 'detector_d_model', self.args.d_model),
            n_heads=getattr(self.args, 'detector_n_heads', self.args.n_heads),
            n_layers=getattr(self.args, 'detector_n_layers', 2),
            dropout=getattr(self.args, 'detector_dropout', self.args.dropout),
            window_size=getattr(self.args, 'window_size', 10)
        )
        return detector.to(self.device)
        
    def _display_strategy_info(self):
        """显示训练策略信息"""
        strategy = getattr(self.args, 'training_strategy', 'basic')
        print(f"📋 训练策略: {strategy.upper()}")
        
        strategy_descriptions = {
            'basic': '基础策略 - 标准训练流程',
            'enhanced': '增强策略 - 课程学习 + 动态调整',
            'aggressive': '激进策略 - 极端不平衡处理'
        }
        
        print(f"📝 策略描述: {strategy_descriptions.get(strategy, '未知策略')}")
        print(f"🎯 优化目标: {getattr(self.args, 'optimize_for', 'f1_score')}")
        print(f"⚖️  异常比例: {getattr(self.args, 'anomaly_ratio', 0.1)}")
        
    def _create_trainer(self, detector: nn.Module) -> UnifiedAnomalyTrainer:
        """创建统一训练器"""
        strategy = getattr(self.args, 'training_strategy', 'basic')
        
        trainer = UnifiedAnomalyTrainer(
            model=detector,
            device=self.device,
            save_dir=self.save_dir,
            patience=getattr(self.args, 'detector_patience', self.args.patience),
            strategy=strategy
        )
        
        # 如果启用了课程学习，传递给训练策略
        if (hasattr(self.args, 'use_curriculum') and self.args.use_curriculum and 
            hasattr(trainer, 'strategy') and hasattr(trainer.strategy, 'use_curriculum')):
            trainer.strategy.use_curriculum = True
            print("✓ 课程学习已启用")
            
        return trainer
        
    def _train_detector(self, trainer: UnifiedAnomalyTrainer, 
                       train_data: KTData, val_data: KTData) -> Dict:
        """执行检测器训练"""
        print(f"\n🚀 开始异常检测器训练...")
        print(f"📊 训练轮数: {getattr(self.args, 'detector_epochs', 30)}")
        print(f"📈 学习率: {getattr(self.args, 'detector_lr', self.args.learning_rate)}")
        
        return trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=getattr(self.args, 'detector_epochs', 30),
            learning_rate=getattr(self.args, 'detector_lr', self.args.learning_rate),
            anomaly_ratio=getattr(self.args, 'anomaly_ratio', 0.1),
            optimize_for=getattr(self.args, 'optimize_for', 'f1_score')
        )
        
    def _save_results(self, metrics: Dict) -> str:
        """保存训练结果"""
        # 保存训练指标
        import json
        metrics_path = os.path.join(self.save_dir, 'detector_metrics.json')
        with open(metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)
            
        # 返回最佳模型路径
        return os.path.join(self.save_dir, 'best_model.pt')
        
    def get_stage_info(self) -> Dict:
        """获取阶段信息"""
        return {
            'stage_number': 2,
            'stage_name': 'detector_training',
            'description': '异常检测器训练',
            'purpose': '训练高质量的异常检测器',
            'output': '训练好的异常检测器模型',
            'dependencies': [],
            'next_stage': 'anomaly_aware_training',
            'supported_strategies': ['basic', 'enhanced', 'aggressive']
        }
