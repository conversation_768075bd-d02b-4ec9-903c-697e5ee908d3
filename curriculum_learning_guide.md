# 课程学习策略使用指南

本文档介绍如何在异常感知知识追踪系统中使用课程学习策略，以提高模型训练效果和泛化能力。

## 什么是课程学习？

课程学习（Curriculum Learning）是一种受人类学习过程启发的训练策略，通过从简单到复杂的顺序逐步增加训练数据的难度，使模型能够更有效地学习。就像人类学习一样，先掌握基础知识，再逐步学习更复杂的概念。

在异常检测任务中，课程学习可以帮助模型：
1. 先学习识别简单的异常模式
2. 逐步适应更复杂、更具挑战性的异常模式
3. 最终能够处理各种复杂的异常情况

## 系统中的课程学习实现

我们的系统通过以下几个方面实现课程学习：

1. **阶段性训练**：将训练过程分为多个阶段（热身、中级、高级、专家）
2. **动态异常比例**：随着训练进行，逐步增加异常样本的比例
3. **难度渐进**：从简单异常模式开始，逐步引入更复杂的异常模式
4. **损失函数调整**：根据训练阶段动态调整损失函数参数和权重

## 如何使用

### 1. 通过配置文件使用

最简单的方式是使用预定义的配置文件：

```bash
python anomaly_aware_kt/scripts/full_pipeline.py --dataset assist17 --config anomaly_aware_kt/configs/curriculum_config.yaml
```

### 2. 通过命令行参数启用

```bash
python anomaly_aware_kt/scripts/full_pipeline.py --dataset assist17 --training_strategy enhanced --use_curriculum
```

### 3. 在代码中直接使用

```python
from anomaly_kt.curriculum import CurriculumScheduler, DifficultyAdjuster
from anomaly_kt.training_strategies import EnhancedStrategy

# 创建课程学习调度器
scheduler = CurriculumScheduler(total_epochs=50)

# 获取当前阶段参数
current_epoch = 10
stage_params = scheduler.get_stage_params(current_epoch)
print(f"当前阶段: {stage_params['name']}")
print(f"异常比例: {stage_params['anomaly_ratio']}")
print(f"难度级别: {stage_params['difficulty']}")
```

## 课程学习配置详解

配置文件（`curriculum_config.yaml`）包含以下主要部分：

### 课程学习阶段定义

```yaml
curriculum_learning:
  enabled: true
  total_epochs: 50
  
  stages:
    - name: warmup
      end_epoch: 10  # 0-10 epochs
      params:
        anomaly_ratio: 0.05  # 低异常比例
        difficulty: easy     # 简单难度
        allowed_types: [random]  # 仅使用随机异常
        focal_gamma: 1.0     # 较低的Focal Loss gamma值
```

每个阶段定义了：
- 阶段名称和结束轮次
- 异常比例
- 难度级别
- 允许的异常类型
- 损失函数参数

### 难度级别参数

```yaml
difficulty_levels:
  easy:
    max_consecutive: 2  # 最大连续异常数
    pattern_size: 2     # 模式大小
    burst_probability: 0.1  # 突发异常概率
```

不同难度级别对应不同的异常生成参数，控制生成异常的复杂度。

## 可视化课程学习进度

系统会自动在训练过程中生成课程学习进度图，保存在输出目录中：

```
output/assist17_20250527_133334/detector/curriculum_progress_epoch_20.png
```

这些图表显示了：
- 异常比例随时间的变化
- 训练阶段的转换
- 难度级别的变化

## 高级定制

如需更高级的定制，可以：

1. 修改 `curriculum.py` 中的 `CurriculumScheduler` 类
2. 创建自定义的课程学习配置文件
3. 在 `training_strategies.py` 中的 `EnhancedStrategy` 类中调整课程学习的使用方式

## 最佳实践

1. **数据集特性**：根据数据集的特点调整课程学习参数。较小的数据集可能需要更温和的课程安排。

2. **训练轮次**：确保总训练轮次足够长，以便完成所有课程阶段。建议至少50轮。

3. **监控指标**：关注每个阶段的性能变化，特别是从一个阶段过渡到下一个阶段时。

4. **阶段调整**：如果发现某个阶段性能下降明显，可以延长该阶段或调整其参数。

## 故障排除

1. **训练不稳定**：如果训练过程不稳定，尝试降低异常比例或使用更温和的难度递增。

2. **过拟合**：如果模型在高级阶段出现过拟合，考虑增加正则化或降低最终阶段的难度。

3. **性能下降**：如果在阶段转换时性能突然下降，可能需要设计更平滑的过渡。

## 参考文献

1. Bengio, Y., et al. (2009). Curriculum learning. In Proceedings of the 26th Annual International Conference on Machine Learning.

2. Kumar, M. P., Packer, B., & Koller, D. (2010). Self-paced learning for latent variable models. In Advances in Neural Information Processing Systems.