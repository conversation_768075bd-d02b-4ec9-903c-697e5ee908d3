#!/usr/bin/env python
"""
从检查点恢复异常感知知识追踪训练

用法:
python scripts/resume_training.py \
    --checkpoint_path path/to/epoch_9.pt \
    --start_epoch 10 \
    --dataset assist09 \
    --detector_path path/to/detector.pt \
    --baseline_path path/to/baseline.pt
"""

import os
import sys
import argparse
import torch
import tomlkit
import yaml
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DTransformer.data import KTData
from anomaly_kt.detector import CausalAnomalyDetector
from anomaly_kt.model import AnomalyAwareDTransformer
from anomaly_kt.enhanced_anomaly_aware_trainer import EnhancedAnomalyAwareTrainer


def prepare_data(dataset_name: str, data_dir: str, batch_size: int, test_batch_size: int):
    """准备数据集"""
    # 加载数据集配置
    datasets = tomlkit.load(open(os.path.join(data_dir, 'datasets.toml')))
    dataset_config = datasets[dataset_name]

    # 创建数据加载器
    train_data = KTData(
        os.path.join(data_dir, dataset_config['train']),
        dataset_config['inputs'],
        batch_size=batch_size,
        shuffle=True
    )

    val_data = KTData(
        os.path.join(data_dir, dataset_config.get('valid', dataset_config['test'])),
        dataset_config['inputs'],
        batch_size=test_batch_size
    )

    return train_data, val_data, dataset_config


def main():
    parser = argparse.ArgumentParser(description='Resume Anomaly-Aware KT Training from Checkpoint')

    # 恢复训练专用参数
    parser.add_argument('--checkpoint_path', help='Path to checkpoint file to resume from')
    parser.add_argument('--start_epoch', type=int, default=1, help='Starting epoch number')

    # 基本参数（与原脚本兼容）
    parser.add_argument('--dataset', required=True, choices=['assist09', 'assist17', 'algebra05', 'statics'])
    parser.add_argument('--detector_path', help='Path to detector model')

    # 兼容原脚本的跳过参数
    parser.add_argument('--skip_baseline', action='store_true', help='Skip baseline training')
    parser.add_argument('--skip_detector', action='store_true', help='Skip detector training')
    parser.add_argument('--baseline_path', help='Path to existing baseline model')

    # 基本参数
    parser.add_argument('--data_dir', default='data')
    parser.add_argument('--output_dir', default=None)
    parser.add_argument('--device', default='cuda' if torch.cuda.is_available() else 'cpu')
    parser.add_argument('-p', '--with_pid', action='store_true')

    # 数据参数
    parser.add_argument('--batch_size', type=int, default=32)
    parser.add_argument('--test_batch_size', type=int, default=64)

    # 模型参数
    parser.add_argument('--d_model', type=int, default=128)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--n_know', type=int, default=16)
    parser.add_argument('--n_layers', type=int, default=3)
    parser.add_argument('--dropout', type=float, default=0.2)
    parser.add_argument('--lambda_cl', type=float, default=0.1)
    parser.add_argument('--proj', action='store_true')
    parser.add_argument('--hard_neg', action='store_true')
    parser.add_argument('--window', type=int, default=1)

    # 异常检测器参数
    parser.add_argument('--detector_d_model', type=int, default=128)
    parser.add_argument('--detector_n_heads', type=int, default=8)
    parser.add_argument('--detector_n_layers', type=int, default=2)
    parser.add_argument('--detector_dropout', type=float, default=0.1)
    parser.add_argument('--window_size', type=int, default=10)

    # 训练参数（兼容原脚本）
    parser.add_argument('--total_epochs', type=int, default=100, help='Total epochs (including already trained)')
    parser.add_argument('--kt_epochs', type=int, default=100, help='KT training epochs (alias for total_epochs)')
    parser.add_argument('--detector_epochs', type=int, default=30, help='Detector epochs (for compatibility)')
    parser.add_argument('--learning_rate', type=float, default=1e-3)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--use_cl', action='store_true')

    # 异常检测参数（兼容原脚本）
    parser.add_argument('--anomaly_ratio', type=float, default=0.1, help='Anomaly ratio (for compatibility)')
    parser.add_argument('--optimize_for', default='f1_score',
                       choices=['f1_score', 'auc_roc', 'recall', 'precision', 'balanced_accuracy', 'mcc'],
                       help='Optimization target (for compatibility)')

    # 异常感知参数
    parser.add_argument('--anomaly_weight', type=float, default=0.5)

    args = parser.parse_args()

    # 参数兼容性处理
    if args.kt_epochs != 100 and args.total_epochs == 100:
        args.total_epochs = args.kt_epochs

    # 检查是否为恢复训练模式
    if args.checkpoint_path and args.start_epoch > 1:
        # 恢复训练模式
        if not os.path.exists(args.checkpoint_path):
            print(f"ERROR: Checkpoint file not found: {args.checkpoint_path}")
            sys.exit(1)
        print(f"✓ Checkpoint found: {args.checkpoint_path}")
        print(f"✓ Will resume from epoch {args.start_epoch}")
    else:
        # 普通训练模式（兼容原脚本）
        print("⚠️  No checkpoint specified, will start normal training from epoch 1")
        args.start_epoch = 1

    # 验证检测器文件
    if args.detector_path:
        if not os.path.exists(args.detector_path):
            print(f"ERROR: Detector file not found: {args.detector_path}")
            sys.exit(1)
        print(f"✓ Detector found: {args.detector_path}")
    else:
        print("ERROR: --detector_path is required")
        sys.exit(1)

    # 验证基线模型（如果需要评估）
    if args.baseline_path:
        if not os.path.exists(args.baseline_path):
            print(f"ERROR: Baseline file not found: {args.baseline_path}")
            sys.exit(1)
        print(f"✓ Baseline found: {args.baseline_path}")

    # 设置输出目录
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output_dir = f"output/{args.dataset}_resume_{timestamp}"

    os.makedirs(args.output_dir, exist_ok=True)

    # 保存配置
    config_save_path = os.path.join(args.output_dir, 'resume_config.yaml')
    with open(config_save_path, 'w') as f:
        yaml.dump(vars(args), f, default_flow_style=False)

    print("\nResume Configuration:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")

    # 准备数据
    train_data, val_data, dataset_config = prepare_data(
        args.dataset, args.data_dir, args.batch_size, args.test_batch_size
    )

    print(f"\n📊 数据集: {args.dataset}")
    print(f"  训练样本: {len(train_data.dataset)}")
    print(f"  验证样本: {len(val_data.dataset)}")

    # 加载异常检测器
    print(f"\n🔍 加载异常检测器...")
    detector = CausalAnomalyDetector(
        n_questions=dataset_config['n_questions'],
        n_pid=dataset_config['n_pid'] if args.with_pid else 0,
        d_model=args.detector_d_model,
        n_heads=args.detector_n_heads,
        n_layers=args.detector_n_layers,
        dropout=args.detector_dropout,
        window_size=args.window_size
    )

    checkpoint = torch.load(args.detector_path, map_location=args.device)
    if 'model_state_dict' in checkpoint:
        detector.load_state_dict(checkpoint['model_state_dict'])
    else:
        detector.load_state_dict(checkpoint)
    detector.to(args.device)
    print("✓ 异常检测器加载完成")

    # 创建异常感知模型
    print(f"\n🧠 创建异常感知模型...")
    model = AnomalyAwareDTransformer(
        n_questions=dataset_config['n_questions'],
        n_pid=dataset_config['n_pid'] if args.with_pid else 0,
        d_model=args.d_model,
        n_heads=args.n_heads,
        n_know=args.n_know,
        n_layers=args.n_layers,
        dropout=args.dropout,
        lambda_cl=args.lambda_cl,
        proj=args.proj,
        hard_neg=args.hard_neg,
        window=args.window,
        anomaly_detector=detector,
        anomaly_weight=args.anomaly_weight
    )

    # 加载检查点（如果有）
    if args.checkpoint_path and args.start_epoch > 1:
        print(f"\n📂 加载检查点...")
        checkpoint = torch.load(args.checkpoint_path, map_location=args.device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print("✓ 模型状态加载完成 (带metadata格式)")
        else:
            model.load_state_dict(checkpoint)
            print("✓ 模型状态加载完成 (直接格式)")
    else:
        print("📂 从头开始训练（无检查点）")

    model.to(args.device)
    print(f"🧠 模型参数: {sum(p.numel() for p in model.parameters()):,}")

    # 创建训练器
    print(f"\n🚀 创建训练器...")
    trainer = EnhancedAnomalyAwareTrainer(
        model=model,
        detector=detector,
        device=args.device,
        save_dir=args.output_dir,
        patience=args.patience,
        use_curriculum=True,
        use_game_theory=True
    )

    # 开始训练
    print(f"\n🎯 从第{args.start_epoch}轮开始训练...")
    remaining_epochs = args.total_epochs - args.start_epoch + 1
    print(f"📊 剩余轮数: {remaining_epochs}")

    anomaly_metrics = trainer.train(
        train_loader=train_data,
        val_loader=val_data,
        epochs=args.total_epochs,
        learning_rate=args.learning_rate,
        initial_anomaly_weight=args.anomaly_weight,
        use_cl=args.use_cl,
        start_epoch=args.start_epoch
    )

    print(f"\n🎉 恢复训练完成!")
    print(f"📈 最终 AUC: {anomaly_metrics['auc']:.4f}")

    # 保存训练总结
    trainer.save_training_summary()


if __name__ == '__main__':
    main()
