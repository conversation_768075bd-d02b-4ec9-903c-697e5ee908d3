# 🎯 异常感知知识追踪训练指标详解

## 📋 概述

本文档详细说明异常感知知识追踪模型训练过程中的各项指标含义，帮助理解模型性能和训练进展。

---

## 🏗️ 训练阶段指标

### **阶段标识**
```
==================== Epoch X/100 - foundation ====================
📋 基础阶段：建立正常行为模式的稳固理解
⚖️  异常权重: 0.100
```

| 阶段 | 轮次范围 | 异常权重 | 训练重点 |
|------|----------|----------|----------|
| **Foundation** | 1-30 | 0.1 | 学习基础知识追踪模式 |
| **Integration** | 31-60 | 0.3 | 逐步引入异常信息 |
| **Refinement** | 61-100 | 0.5 | 全谱异常感知与平衡 |

---

## 📊 训练损失指标

### **总体损失分解**
```
📈 训练 - Loss: 0.5174
  KT Loss: 0.6227, Anomaly Loss: 0.0063
```

#### **Loss (总损失)**
- **定义**: 模型的综合训练损失
- **组成**: KT损失 + 异常损失 + 正则化损失
- **趋势**: 应该随训练轮次逐渐下降
- **理想值**: < 0.3（经验值）

#### **KT Loss (知识追踪损失)**
- **定义**: 标准知识追踪预测的损失
- **计算**: 基于学生答题预测的交叉熵损失
- **意义**: 反映模型基础预测能力
- **Foundation阶段**: 占主导地位（权重高）

#### **Anomaly Loss (异常损失)**
- **定义**: 博弈论异常处理的损失
- **特点**: Foundation阶段很小（0.006左右）
- **作用**: 训练模型处理异常情况的能力
- **发展**: 随阶段推进逐渐增大

---

## 🎯 验证性能指标

### **基础性能指标**
```
📊 验证 - ACC: 0.674, AUC: 0.668, MAE: 0.420
```

#### **ACC (准确率)**
- **定义**: 预测正确的样本比例
- **计算**: (正确预测数) / (总预测数)
- **范围**: 0.0 - 1.0
- **解读**:
  - 0.9+ : 优秀
  - 0.8-0.9 : 良好
  - 0.7-0.8 : 一般
  - < 0.7 : 需要改进

#### **AUC (ROC曲线下面积)**
- **定义**: 模型区分能力的综合指标
- **范围**: 0.0 - 1.0
- **解读**:
  - 0.9+ : 优秀
  - 0.8-0.9 : 良好
  - 0.7-0.8 : 一般
  - 0.5-0.7 : 较差
  - 0.5 : 随机猜测水平

#### **MAE (平均绝对误差)**
- **定义**: 预测概率与真实标签的平均偏差
- **计算**: (1/n) × Σ|预测值 - 真实值|
- **范围**: 0.0 - 0.5
- **解读**:
  - 0.0-0.2 : 优秀
  - 0.2-0.3 : 良好
  - 0.3-0.4 : 一般
  - 0.4-0.5 : 较差
- **注意**: 数值越小越好

---

## 🔍 异常检测统计

### **异常分数分析**
```
🔍 异常检测统计:
  异常分数均值: 0.234
  异常分数标准差: 0.156
  >0.5的样本比例: 15.2%
  正常样本数: 181495
  异常样本数: 211
```

#### **异常分数均值**
- **定义**: 所有样本异常分数的平均值
- **正常范围**: 0.0 - 1.0
- **理想值**: 0.1 - 0.3（大部分样本为正常）
- **异常情况**: 负值表示模型输出错误

#### **异常分数标准差**
- **定义**: 异常分数的离散程度
- **意义**: 
  - 高标准差: 模型能区分正常/异常
  - 低标准差: 模型输出趋于一致
- **理想值**: 0.2 - 0.4

#### **>0.5的样本比例**
- **定义**: 被识别为异常的样本比例
- **Foundation阶段**: 应该很低（< 5%）
- **Integration阶段**: 逐渐增加（5-15%）
- **Refinement阶段**: 稳定在合理水平（10-20%）

#### **样本数量分布**
- **正常样本数**: 异常分数 ≤ 0.5 的样本数量
- **异常样本数**: 异常分数 > 0.5 的样本数量
- **比例**: 正常样本应占大多数（80%+）

---

## ⚖️ 异常影响分析

### **核心指标**
```
🔍 异常影响:
  性能差距: 0.091
  相对影响: 21.7%
```

#### **性能差距 (Performance Gap)**
- **定义**: 正常样本MAE - 异常样本MAE
- **计算**: normal_mae - anomaly_mae
- **解读**:
  - **正值**: 异常样本表现更差
  - **负值**: 异常样本表现更好（异常）
  - **接近0**: 两者表现相似
- **理想值**: 
  - Foundation阶段: 0.0 - 0.05
  - Integration阶段: 0.05 - 0.10
  - Refinement阶段: 0.08 - 0.15

#### **相对影响 (Relative Impact)**
- **定义**: 异常对整体性能的相对影响程度
- **计算**: |性能差距| / 整体MAE × 100%
- **解读**:
  - **< 5%**: 异常影响很小，模型鲁棒
  - **5-15%**: 异常影响适中，需要调整
  - **15-30%**: 异常影响较大，需要优化
  - **> 30%**: 异常影响严重，策略有问题
- **阶段预期**:
  - Foundation: < 10%
  - Integration: 10-20%
  - Refinement: 15-25%

---

## 📈 训练进展评估

### **健康训练的特征**

#### **Foundation阶段 (1-30轮)**
- ✅ 异常权重低 (0.1)
- ✅ KT损失占主导
- ✅ 异常损失很小 (< 0.01)
- ✅ AUC稳步提升
- ✅ 相对影响 < 15%

#### **Integration阶段 (31-60轮)**
- ✅ 异常权重中等 (0.3)
- ✅ 异常损失增加
- ✅ 性能差距出现但可控
- ✅ 相对影响 10-20%

#### **Refinement阶段 (61-100轮)**
- ✅ 异常权重高 (0.5)
- ✅ 异常感知能力成熟
- ✅ 整体性能优于基线
- ✅ 相对影响稳定

### **问题诊断指南**

#### **🚨 异常分数为负值**
- **原因**: 检测器输出未经sigmoid激活
- **解决**: 确保 `anomaly_scores = torch.sigmoid(logits)`

#### **🚨 相对影响过高 (>30%)**
- **原因**: 异常权重过大或检测器过敏感
- **解决**: 降低异常权重或调整检测阈值

#### **🚨 异常样本过少 (<1%)**
- **原因**: 检测器阈值过高
- **解决**: 降低异常判断阈值（从0.5到0.3）

#### **🚨 训练损失不下降**
- **原因**: 学习率过高或博弈论损失冲突
- **解决**: 降低学习率或调整损失权重

---

## 🎯 性能目标设定

### **短期目标 (10-20轮)**
- AUC > 0.70
- MAE < 0.35
- 相对影响 < 20%

### **中期目标 (30-50轮)**
- AUC > 0.75
- MAE < 0.30
- 相对影响 < 15%

### **最终目标 (80-100轮)**
- AUC > 0.80
- MAE < 0.25
- 相对影响 < 10%
- 比基线模型提升 ≥ 1%

---

## 📝 监控建议

1. **每轮关注**: AUC变化趋势
2. **每5轮检查**: 异常影响是否合理
3. **每阶段评估**: 是否达到阶段目标
4. **异常情况**: 立即检查异常分数分布

通过这些指标的综合分析，可以有效监控异常感知知识追踪模型的训练进展，及时发现和解决问题。
