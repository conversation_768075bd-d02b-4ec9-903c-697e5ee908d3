"""
科学异常生成器模块

基于认知科学理论和IRT难度建模的异常生成器框架，包含：
1. 基础异常策略接口
2. 科学异常生成器主类
3. 异常生成器工厂
4. 质量控制集成
"""

from .base_strategy import BaseAnomalyStrategy
from .scientific_generator import ScientificAnomalyGenerator
from .generator_factory import AnomalyGeneratorFactory
from .strategy_implementations import (
    CognitiveLoadStrategy,
    MetacognitiveStrategy,
    MotivationalStrategy,
    ExternalInterferenceStrategy
)

__all__ = [
    "BaseAnomalyStrategy",
    "ScientificAnomalyGenerator", 
    "AnomalyGeneratorFactory",
    "CognitiveLoadStrategy",
    "MetacognitiveStrategy",
    "MotivationalStrategy", 
    "ExternalInterferenceStrategy",
]
