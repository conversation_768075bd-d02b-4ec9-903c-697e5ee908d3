#!/usr/bin/env python
"""
增强异常检测器训练脚本
支持从检查点继续训练
支持从配置文件加载参数
"""

import os
import sys
import argparse
import torch
import tomlkit
import yaml
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DTransformer.data import KTData
from anomaly_kt.detector import CausalAnomalyDetector
from anomaly_kt.unified_trainer import UnifiedAnomalyTrainer


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载YAML配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    print(f"📄 加载配置文件: {config_path}")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def find_latest_checkpoint(checkpoint_dir: str) -> tuple:
    """
    查找指定目录中的最新检查点
    
    Args:
        checkpoint_dir: 检查点目录路径
        
    Returns:
        最新检查点的路径和轮次信息
    """
    print(f"🔍 在 {checkpoint_dir} 中查找最新检查点...")
    
    all_checkpoints = []
    
    for root, dirs, files in os.walk(checkpoint_dir):
        for file in files:
            if file.endswith('.pt'):
                full_path = os.path.join(root, file)
                try:
                    checkpoint = torch.load(full_path, map_location='cpu')
                    epoch = checkpoint.get('epoch', 0)
                    metrics = checkpoint.get('metrics', {})
                    
                    all_checkpoints.append({
                        'path': full_path,
                        'epoch': epoch,
                        'metrics': metrics,
                        'mtime': os.path.getmtime(full_path)
                    })
                except Exception as e:
                    print(f"  ⚠️ 无法加载检查点 {full_path}: {e}")
                    continue
    
    if not all_checkpoints:
        return None, 0
    
    # 按轮次排序
    all_checkpoints.sort(key=lambda x: x['epoch'], reverse=True)
    latest = all_checkpoints[0]
    
    print(f"  ✅ 找到最新检查点: {latest['path']}")
    print(f"  📊 轮次: {latest['epoch']}, 指标: {latest['metrics']}")
    
    return latest['path'], latest['epoch']


def train_detector(config: Dict[str, Any]) -> Dict[str, float]:
    """
    训练或继续训练异常检测器
    
    Args:
        config: 配置字典
        
    Returns:
        训练完成的最佳指标
    """
    print("\n" + "="*60)
    print("训练异常检测器 (Enhanced Strategy)")
    print("="*60)
    
    # 提取配置参数
    dataset_name = config['dataset']['name']
    data_dir = config['dataset']['data_dir']
    batch_size = int(config['dataset']['batch_size'])
    test_batch_size = int(config['dataset'].get('test_batch_size', batch_size))
    with_pid = bool(config['dataset'].get('with_pid', False))
    
    d_model = int(config['model']['detector']['d_model'])
    n_heads = int(config['model']['detector']['n_heads'])
    n_layers = int(config['model']['detector']['n_layers'])
    dropout = float(config['model']['detector']['dropout'])
    window_size = int(config['model']['detector']['window_size'])
    
    strategy = config['training']['strategy']
    total_epochs = int(config['training']['total_epochs'])
    learning_rate = float(config['training']['learning_rate'])
    anomaly_ratio = float(config['training']['anomaly_ratio'])
    val_anomaly_ratio = float(config['training'].get('val_anomaly_ratio', anomaly_ratio))
    optimize_for = config['training']['optimize_for']
    patience = int(config['training']['patience'])
    
    continue_enabled = config['continue_training']['enabled']
    checkpoint_dir = config['continue_training']['checkpoint_dir']
    
    device = config['device']
    save_dir = config['save_dir']
    
    # 加载数据集配置
    datasets = tomlkit.load(open(os.path.join(data_dir, 'datasets.toml')))
    dataset_config = datasets[dataset_name]
    
    print(f"📊 数据集: {dataset_name}")
    print(f"  问题数: {dataset_config['n_questions']}")
    print(f"  知识点数: {dataset_config['n_pid']}")
    
    # 准备数据
    train_data = KTData(
        os.path.join(data_dir, dataset_config['train']),
        dataset_config['inputs'],
        batch_size=batch_size,
        shuffle=True
    )
    
    val_data = KTData(
        os.path.join(data_dir, dataset_config.get('valid', dataset_config['test'])),
        dataset_config['inputs'],
        batch_size=test_batch_size
    )
    
    print(f"📁 训练批次: {len(train_data)}, 验证批次: {len(val_data)}")
    
    # 设置保存目录
    if save_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if continue_enabled:
            save_dir = f"output/continue_{dataset_name}_{timestamp}/detector"
        else:
            save_dir = f"output/{dataset_name}_{timestamp}/detector"
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建模型
    detector = CausalAnomalyDetector(
        n_questions=dataset_config['n_questions'],
        n_pid=dataset_config['n_pid'] if with_pid else 0,
        d_model=d_model,
        n_heads=n_heads,
        n_layers=n_layers,
        dropout=dropout,
        window_size=window_size
    )
    
    # 如果继续训练，加载检查点
    start_epoch = 0
    if continue_enabled:
        if not checkpoint_dir:
            print("❌ 继续训练需要指定检查点目录")
            return None
            
        checkpoint_path, start_epoch = find_latest_checkpoint(checkpoint_dir)
        if checkpoint_path:
            print(f"📥 加载检查点: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            detector.load_state_dict(checkpoint['model_state_dict'])
            print(f"📊 从第 {start_epoch} 轮继续训练")
        else:
            print("❌ 未找到有效检查点，将从头开始训练")
    
    detector = detector.to(device)
    print(f"🧠 模型参数: {sum(p.numel() for p in detector.parameters()):,}")
    
    # 创建训练器
    trainer = UnifiedAnomalyTrainer(
        model=detector,
        device=device,
        save_dir=save_dir,
        patience=patience,
        strategy=strategy
    )
    
    # 计算剩余轮数
    remaining_epochs = total_epochs
    if continue_enabled and start_epoch > 0:
        remaining_epochs = max(1, total_epochs - start_epoch)
        print(f"📊 已训练轮数: {start_epoch}, 剩余轮数: {remaining_epochs}")
    
    # 训练参数
    train_config = {
        'epochs': remaining_epochs,
        'learning_rate': learning_rate,
        'anomaly_ratio': anomaly_ratio,
        'val_anomaly_ratio': val_anomaly_ratio,  # 已经转换为float类型
        'optimize_for': optimize_for,
        'start_epoch': start_epoch if continue_enabled else 0
    }
    
    # 开始训练
    print(f"\n🚀 开始训练...")
    print(f"📊 总轮数: {total_epochs}, 当前轮数: {start_epoch}, 剩余轮数: {remaining_epochs}")
    print(f"🎯 优化目标: {optimize_for}")
    
    # 训练
    best_metrics = trainer.train(
        train_loader=train_data,
        val_loader=val_data,
        **train_config
    )
    
    print("\n🎉 训练完成!")
    print("📊 最终结果:")
    for metric, value in best_metrics.items():
        if isinstance(value, (int, float)):
            print(f"  {metric}: {value:.4f}")
    
    return best_metrics


def config_from_args(args: argparse.Namespace) -> Dict[str, Any]:
    """
    从命令行参数创建配置字典
    
    Args:
        args: 命令行参数
        
    Returns:
        配置字典
    """
    config = {
        'dataset': {
            'name': args.dataset,
            'data_dir': args.data_dir,
            'batch_size': args.batch_size,
            'test_batch_size': args.test_batch_size,
            'with_pid': args.with_pid
        },
        'model': {
            'detector': {
                'd_model': args.d_model,
                'n_heads': args.n_heads,
                'n_layers': args.n_layers,
                'dropout': args.dropout,
                'window_size': args.window_size
            }
        },
        'training': {
            'strategy': 'enhanced',  # 固定使用增强策略
            'total_epochs': args.total_epochs,
            'learning_rate': args.learning_rate,
            'anomaly_ratio': args.anomaly_ratio,
            'val_anomaly_ratio': args.val_anomaly_ratio,
            'optimize_for': args.optimize_for,
            'patience': args.patience
        },
        'continue_training': {
            'enabled': args.continue_training,
            'checkpoint_dir': args.checkpoint_dir
        },
        'device': args.device,
        'save_dir': args.save_dir
    }
    return config


def main():
    parser = argparse.ArgumentParser(description='Enhanced Anomaly Detector Training')
    
    # 配置文件参数
    parser.add_argument('--config', help='配置文件路径')
    
    # 基本参数
    parser.add_argument('-d', '--dataset', choices=['assist09', 'assist17', 'algebra05', 'statics'],
                       help='数据集名称')
    parser.add_argument('--data_dir', default='data', help='数据目录')
    parser.add_argument('-p', '--with_pid', action='store_true', help='使用问题ID')
    parser.add_argument('--device', default='cuda' if torch.cuda.is_available() else 'cpu',
                       help='训练设备')
    parser.add_argument('--save_dir', help='保存目录')
    
    # 继续训练参数
    parser.add_argument('--continue', dest='continue_training', action='store_true',
                       help='从检查点继续训练')
    parser.add_argument('--checkpoint_dir', help='检查点目录路径')
    
    # 数据参数
    parser.add_argument('--batch_size', type=int, default=32, help='训练批次大小')
    parser.add_argument('--test_batch_size', type=int, help='测试批次大小')
    
    # 模型参数
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--n_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--n_layers', type=int, default=2, help='层数')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    parser.add_argument('--window_size', type=int, default=15, help='窗口大小')
    
    # 训练参数
    parser.add_argument('--total_epochs', type=int, default=50, help='总训练轮数')
    parser.add_argument('--learning_rate', type=float, default=5e-4, help='学习率')
    parser.add_argument('--anomaly_ratio', type=float, default=0.3, help='训练异常比例')
    parser.add_argument('--val_anomaly_ratio', type=float, help='验证异常比例')
    parser.add_argument('--optimize_for', default='recall',
                         choices=['recall', 'precision', 'f1_score', 'auc_roc'],
                         help='优化目标')
    parser.add_argument('--patience', type=int, default=15, help='早停耐心值')
    
    # 兼容性参数（不使用但保留）
    parser.add_argument('--use_cl', action='store_true', help='使用对比学习')
    parser.add_argument('--proj', action='store_true', help='使用投影')
    parser.add_argument('--n_know', type=int, default=32, help='知识点数量')
    
    args = parser.parse_args()
    
    # 默认配置文件路径
    default_config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                      'configs', 'enhanced_trainer_config.yaml')
    
    # 加载配置
    if args.config:
        # 从指定的配置文件加载
        config_path = args.config
    elif os.path.exists(default_config_path):
        # 从默认配置文件加载
        config_path = default_config_path
        print(f"📄 使用默认配置文件: {default_config_path}")
    else:
        # 从命令行参数创建配置
        if args.continue_training and not args.checkpoint_dir:
            print("ERROR: --continue requires --checkpoint_dir to be specified")
            return 1
        if not args.dataset:
            print("ERROR: --dataset is required when not using a config file")
            return 1
            
        config = config_from_args(args)
        
    # 如果使用配置文件
    if 'config_path' in locals():
        # 从配置文件加载
        config = load_config(config_path)
        
        # 命令行参数覆盖配置文件
        if args.dataset:
            config['dataset']['name'] = args.dataset
        if args.checkpoint_dir:
            config['continue_training']['checkpoint_dir'] = args.checkpoint_dir
            config['continue_training']['enabled'] = True
        if args.val_anomaly_ratio is not None:
            config['training']['val_anomaly_ratio'] = float(args.val_anomaly_ratio)
    
    # 打印配置
    print("⚙️  配置参数:")
    print(f"  数据集: {config['dataset']['name']}")
    print(f"  批次大小: {config['dataset']['batch_size']}")
    print(f"  训练策略: {config['training']['strategy']}")
    print(f"  总轮数: {config['training']['total_epochs']}")
    print(f"  优化目标: {config['training']['optimize_for']}")
    print(f"  继续训练: {config['continue_training']['enabled']}")
    if config['continue_training']['enabled']:
        print(f"  检查点目录: {config['continue_training']['checkpoint_dir']}")
    
    # 保存配置
    if not os.path.exists(config['save_dir'] or 'output'):
        os.makedirs(config['save_dir'] or 'output', exist_ok=True)
    
    # 设置随机种子
    torch.manual_seed(42)
    
    # 训练
    try:
        train_detector(config)
        print(f"\n✅ 训练成功完成!")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())