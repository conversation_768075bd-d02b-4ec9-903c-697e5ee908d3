"""
基础异常策略接口

定义所有异常生成策略的统一接口，确保策略的可插拔性和一致性。
每个策略都基于特定的认知科学理论，并集成IRT难度建模。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any, Union
import torch
import numpy as np
from dataclasses import dataclass
from enum import Enum

from ..taxonomy.base_anomaly import AnomalyCategory, AnomalySeverity, AnomalyContext
from ..irt_modeling.irt_model import IRTDifficultyModel, IRTParameters


class StrategyType(Enum):
    """策略类型枚举"""
    COGNITIVE_LOAD = "cognitive_load"
    METACOGNITIVE = "metacognitive"
    MOTIVATIONAL = "motivational"
    EXTERNAL_INTERFERENCE = "external_interference"


@dataclass
class GenerationContext:
    """异常生成上下文"""
    # 基础数据
    questions: torch.Tensor          # 问题ID序列
    responses: torch.Tensor          # 原始响应序列
    difficulties: torch.Tensor       # 问题难度
    
    # 可选的额外信息
    problem_ids: Optional[torch.Tensor] = None      # 问题类型ID
    interaction_types: Optional[torch.Tensor] = None # 交互类型
    answer_times: Optional[torch.Tensor] = None     # 答题时间
    knowledge_states: Optional[torch.Tensor] = None # 知识状态
    
    # 上下文信息
    sequence_position: float = 0.0   # 在整个学习序列中的位置 (0-1)
    session_info: Optional[Dict] = None
    student_profile: Optional[Dict] = None
    
    def to_anomaly_context(self) -> AnomalyContext:
        """转换为异常上下文"""
        return AnomalyContext(
            sequence_length=len(self.responses),
            difficulty_profile=self.difficulties,
            knowledge_state=self.knowledge_states,
            temporal_position=self.sequence_position,
            session_info=self.session_info,
            student_profile=self.student_profile
        )


@dataclass
class GenerationResult:
    """异常生成结果"""
    anomaly_sequence: torch.Tensor    # 包含异常的序列
    anomaly_mask: torch.Tensor        # 异常位置掩码
    anomaly_info: Dict[str, Any]      # 异常信息
    quality_metrics: Dict[str, float] # 质量指标
    
    def get_anomaly_ratio(self) -> float:
        """获取实际异常比例"""
        total_positions = (self.anomaly_sequence >= 0).sum().item()
        anomaly_count = self.anomaly_mask.sum().item()
        return anomaly_count / total_positions if total_positions > 0 else 0.0


class BaseAnomalyStrategy(ABC):
    """
    基础异常策略抽象类
    
    定义所有异常生成策略的统一接口，确保：
    1. 理论基础的一致性
    2. 生成质量的可控性
    3. 策略的可组合性
    """
    
    def __init__(self, 
                 config: Dict[str, Any],
                 irt_model: Optional[IRTDifficultyModel] = None):
        """
        初始化异常策略
        
        Args:
            config: 策略配置参数
            irt_model: IRT难度模型（可选）
        """
        self.config = config
        self.irt_model = irt_model
        self.strategy_type = self._get_strategy_type()
        self.theoretical_foundation = self._get_theoretical_foundation()
        
        # 策略参数
        self.intensity_range = config.get('intensity_range', (0.1, 0.9))
        self.duration_range = config.get('duration_range', (3, 15))
        self.probability_threshold = config.get('probability_threshold', 0.5)
        
        # 质量控制参数
        self.quality_threshold = config.get('quality_threshold', 0.7)
        self.max_retries = config.get('max_retries', 3)
        
    @abstractmethod
    def _get_strategy_type(self) -> StrategyType:
        """获取策略类型"""
        pass
        
    @abstractmethod
    def _get_theoretical_foundation(self) -> Dict[str, str]:
        """获取理论基础"""
        pass
        
    @abstractmethod
    def generate_anomalies(self,
                          context: GenerationContext,
                          target_ratio: float = 0.15,
                          severity: AnomalySeverity = AnomalySeverity.MEDIUM) -> GenerationResult:
        """
        生成异常
        
        Args:
            context: 生成上下文
            target_ratio: 目标异常比例
            severity: 异常严重程度
            
        Returns:
            GenerationResult: 生成结果
        """
        pass
        
    @abstractmethod
    def validate_generation(self,
                           original: torch.Tensor,
                           anomaly: torch.Tensor,
                           mask: torch.Tensor,
                           context: GenerationContext) -> Dict[str, float]:
        """
        验证生成质量
        
        Args:
            original: 原始序列
            anomaly: 异常序列
            mask: 异常掩码
            context: 生成上下文
            
        Returns:
            Dict[str, float]: 质量指标
        """
        pass
        
    def estimate_difficulty_with_irt(self, 
                                   questions: torch.Tensor,
                                   responses: torch.Tensor) -> torch.Tensor:
        """
        使用IRT模型估计难度
        
        Args:
            questions: 问题ID序列
            responses: 响应序列
            
        Returns:
            torch.Tensor: 难度估计
        """
        if self.irt_model is None:
            # 回退到简单的基于正确率的估计
            return self._simple_difficulty_estimation(questions, responses)
            
        try:
            # 使用IRT模型估计难度
            if not self.irt_model.is_fitted:
                # 如果模型未拟合，使用当前数据进行快速校准
                self.irt_model.calibrate_items(responses.unsqueeze(0))
                
            # 获取问题对应的难度参数
            unique_questions = torch.unique(questions)
            difficulty_map = {}
            
            if self.irt_model.parameters is not None:
                for i, q_id in enumerate(unique_questions):
                    if i < len(self.irt_model.parameters.difficulty):
                        difficulty_map[q_id.item()] = self.irt_model.parameters.difficulty[i].item()
                        
            # 映射到序列
            difficulties = torch.zeros_like(questions, dtype=torch.float)
            for i, q_id in enumerate(questions):
                difficulties[i] = difficulty_map.get(q_id.item(), 0.0)
                
            return difficulties
            
        except Exception as e:
            print(f"Warning: IRT difficulty estimation failed: {e}")
            return self._simple_difficulty_estimation(questions, responses)
            
    def _simple_difficulty_estimation(self, 
                                    questions: torch.Tensor,
                                    responses: torch.Tensor) -> torch.Tensor:
        """简单的基于正确率的难度估计"""
        unique_questions = torch.unique(questions)
        difficulty_map = {}
        
        for q_id in unique_questions:
            q_mask = questions == q_id
            q_responses = responses[q_mask]
            valid_responses = q_responses[q_responses >= 0]
            
            if len(valid_responses) > 0:
                correct_rate = valid_responses.float().mean().item()
                # 转换为难度：正确率越低，难度越高
                difficulty = 1 - correct_rate
            else:
                difficulty = 0.5  # 默认中等难度
                
            difficulty_map[q_id.item()] = difficulty
            
        # 映射到序列
        difficulties = torch.zeros_like(questions, dtype=torch.float)
        for i, q_id in enumerate(questions):
            difficulties[i] = difficulty_map.get(q_id.item(), 0.5)
            
        return difficulties
        
    def _calculate_adaptive_intensity(self,
                                    context: GenerationContext,
                                    severity: AnomalySeverity) -> float:
        """计算自适应异常强度"""
        base_intensity = {
            AnomalySeverity.LOW: 0.3,
            AnomalySeverity.MEDIUM: 0.6,
            AnomalySeverity.HIGH: 0.8,
            AnomalySeverity.EXTREME: 0.95
        }[severity]
        
        # 根据上下文调整强度
        adjustment_factor = 1.0
        
        # 1. 序列位置影响
        if context.sequence_position > 0.7:
            adjustment_factor *= 1.2  # 后期增强
        elif context.sequence_position < 0.3:
            adjustment_factor *= 0.8  # 前期减弱
            
        # 2. 平均难度影响
        avg_difficulty = context.difficulties.mean().item()
        if avg_difficulty > 0.7:
            adjustment_factor *= 1.1  # 高难度增强
        elif avg_difficulty < 0.3:
            adjustment_factor *= 0.9  # 低难度减弱
            
        # 3. 知识状态影响（如果可用）
        if context.knowledge_states is not None:
            avg_knowledge = context.knowledge_states.mean().item()
            if avg_knowledge < 0.3:
                adjustment_factor *= 1.2  # 低知识水平增强异常
                
        # 应用范围约束
        final_intensity = base_intensity * adjustment_factor
        return max(self.intensity_range[0], min(self.intensity_range[1], final_intensity))
        
    def _select_anomaly_positions(self,
                                sequence_length: int,
                                target_ratio: float,
                                context: GenerationContext) -> List[int]:
        """选择异常位置"""
        target_count = max(1, int(sequence_length * target_ratio))
        
        # 基础策略：随机选择
        available_positions = list(range(sequence_length))
        
        # 根据策略类型调整选择概率
        position_weights = self._calculate_position_weights(available_positions, context)
        
        # 加权随机选择
        if len(position_weights) > 0:
            probabilities = np.array(position_weights)
            probabilities = probabilities / probabilities.sum()
            
            selected_positions = np.random.choice(
                available_positions,
                size=min(target_count, len(available_positions)),
                replace=False,
                p=probabilities
            ).tolist()
        else:
            selected_positions = np.random.choice(
                available_positions,
                size=min(target_count, len(available_positions)),
                replace=False
            ).tolist()
            
        return sorted(selected_positions)
        
    def _calculate_position_weights(self,
                                  positions: List[int],
                                  context: GenerationContext) -> List[float]:
        """计算位置权重（子类可重写）"""
        # 默认均匀权重
        return [1.0] * len(positions)
        
    def _apply_anomaly_transformation(self,
                                    original_response: int,
                                    position: int,
                                    context: GenerationContext,
                                    intensity: float) -> int:
        """应用异常变换（子类可重写）"""
        # 默认简单翻转
        if torch.rand(1).item() < intensity:
            return 1 - original_response
        return original_response
        
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'strategy_type': self.strategy_type.value,
            'theoretical_foundation': self.theoretical_foundation,
            'config': self.config,
            'intensity_range': self.intensity_range,
            'duration_range': self.duration_range,
            'quality_threshold': self.quality_threshold
        }
        
    def _ensure_quality(self,
                       result: GenerationResult,
                       context: GenerationContext,
                       min_quality: float = 0.7) -> GenerationResult:
        """确保生成质量"""
        current_quality = np.mean(list(result.quality_metrics.values()))
        
        if current_quality >= min_quality:
            return result
            
        # 如果质量不足，尝试重新生成
        for retry in range(self.max_retries):
            print(f"Quality insufficient ({current_quality:.3f}), retrying {retry+1}/{self.max_retries}")
            
            # 调整参数重新生成
            adjusted_context = self._adjust_context_for_quality(context, retry)
            new_result = self.generate_anomalies(adjusted_context)
            new_quality = np.mean(list(new_result.quality_metrics.values()))
            
            if new_quality > current_quality:
                result = new_result
                current_quality = new_quality
                
            if current_quality >= min_quality:
                break
                
        return result
        
    def _adjust_context_for_quality(self,
                                   context: GenerationContext,
                                   retry_count: int) -> GenerationContext:
        """为提高质量调整上下文（子类可重写）"""
        # 默认不调整
        return context
