"""
异常感知知识追踪训练流程的各个阶段

将训练流程分解为4个独立的阶段，便于理解和维护：
1. Stage1: 基线DTransformer模型训练
2. Stage2: 异常检测器训练  
3. Stage3: 异常感知知识追踪训练
4. Stage4: 模型评估与对比
"""

from .stage1_baseline import BaselineTrainingStage
from .stage2_detector import DetectorTrainingStage
from .stage3_anomaly_aware import AnomalyAwareTrainingStage
from .stage4_evaluation import EvaluationStage

__all__ = [
    "BaselineTrainingStage",
    "DetectorTrainingStage", 
    "AnomalyAwareTrainingStage",
    "EvaluationStage",
]
