# 课程学习配置文件
# 用于异常感知知识追踪模型的训练

curriculum_learning:
  enabled: true
  total_epochs: 50
  
  # 课程学习阶段定义
  stages:
    - name: warmup
      end_epoch: 10  # 0-10 epochs
      params:
        anomaly_ratio: 0.05
        difficulty: easy
        allowed_types: [random]
        focal_gamma: 1.0
        loss_weights:
          bce: 1.0
          focal: 0.0
          contrastive: 0.0
    
    - name: intermediate
      end_epoch: 25  # 11-25 epochs
      params:
        anomaly_ratio: 0.15
        difficulty: medium
        allowed_types: [random, pattern]
        focal_gamma: 1.5
        loss_weights:
          bce: 0.7
          focal: 0.3
          contrastive: 0.0
    
    - name: advanced
      end_epoch: 40  # 26-40 epochs
      params:
        anomaly_ratio: 0.25
        difficulty: hard
        allowed_types: [random, pattern, difficulty]
        focal_gamma: 2.0
        loss_weights:
          bce: 0.3
          focal: 0.5
          contrastive: 0.2
    
    - name: expert
      end_epoch: 50  # 41-50 epochs
      params:
        anomaly_ratio: 0.3
        difficulty: expert
        allowed_types: all
        focal_gamma: 2.5
        loss_weights:
          bce: 0.0
          focal: 0.7
          contrastive: 0.3

# 难度级别参数定义
difficulty_levels:
  easy:
    max_consecutive: 2
    pattern_size: 2
    burst_probability: 0.1
    max_pattern_repeat: 2
  
  medium:
    max_consecutive: 3
    pattern_size: 3
    burst_probability: 0.2
    max_pattern_repeat: 3
  
  hard:
    max_consecutive: 5
    pattern_size: 4
    burst_probability: 0.3
    max_pattern_repeat: 4
  
  expert:
    max_consecutive: 8
    pattern_size: 5
    burst_probability: 0.4
    max_pattern_repeat: 5

# 训练策略配置
training:
  strategy: enhanced  # 使用增强策略
  use_curriculum: true
  base_anomaly_ratio: 0.3  # 如果不使用课程学习，则使用此比例
  learning_rate: 5e-4
  weight_decay: 1e-4
  patience: 15
  gradient_clip: 1.0