# 快速测试配置 - 用于快速验证和调试
# 适用场景：代码验证、快速原型、调试

# 基本配置
data_dir: "data"
device: "auto"
with_pid: true

# 数据参数 - 使用较大批次加速训练
batch_size: 64
test_batch_size: 128

# 基线模型参数 - 较小的模型
d_model: 64
n_heads: 4
n_know: 16
n_layers: 2
dropout: 0.3
lambda_cl: 0.1
proj: true
hard_neg: false
window: 1

# 异常检测器参数 - 简化配置
detector_d_model: 64
detector_n_heads: 4
detector_n_layers: 1
detector_dropout: 0.2
window_size: 5
anomaly_ratio: 0.1
optimize_for: "f1_score"

# 训练参数 - 快速训练
kt_epochs: 20
detector_epochs: 15
learning_rate: 2e-3  # 稍高的学习率加速收敛
detector_lr: 2e-3
patience: 5
detector_patience: 5
use_cl: true

# 异常感知参数 - 简化配置
anomaly_weight: 0.1
use_enhanced_anomaly_aware: true
use_aa_curriculum: false  # 关闭以加速训练
use_aa_game_theory: false  # 关闭以加速训练

# 训练策略 - 使用基础策略
training_strategy: "basic"
use_curriculum: false

# 控制参数
skip_baseline: false
skip_detector: false
skip_anomaly_training: false
