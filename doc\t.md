DTransformer 半监督知识追踪实现方案文档
1. 背景与目标
知识追踪（KT）任务中，高质量标注数据（学生答题序列的正确 / 错误标签）通常稀缺。半监督学习通过结合少量标注数据与大量未标注数据，可提升模型泛化能力。本方案基于用户现有代码库（DTransformer 模型），设计适用于其数据集（如statics、assist09等）的半监督实现方案，核心思路是利用对比学习（CL）对齐标注与未标注数据的知识表示，并通过伪标签技术挖掘未标注数据的监督信号。
2. 数据集适配说明
用户现有数据集结构（以statics为例）：
文件格式：每行包含数值序列（问题 / 知识点 ID）、标签序列（0/1 表示答题结果）、元数据（如样本长度）。
输入字段：通过datasets.toml配置，如statics的输入为["q", "s"]（q为问题 ID，s为答题状态）。
未标注数据定义：未标注样本指s标签缺失（或标记为-1）的答题序列，仅包含q（问题 ID 序列）。
3. 半监督核心策略
结合用户代码库中的 DTransformer 模型，采用以下半监督策略：
3.1 对比学习（CL）增强表示
利用 DTransformer 已有的对比学习模块（get_cl_loss方法），对未标注数据进行 ** 弱增强（如序列元素交换）** 生成正样本，** 强增强（如标签翻转、随机掩码）** 生成负样本，通过对比损失（InfoNCE）对齐同一数据的不同视图表示，强化模型对知识模式的鲁棒性。
3.2 伪标签生成与筛选
对未标注数据，使用当前模型生成伪标签（s_pseudo），并筛选高置信度样本（如预测概率 > 0.9 或 < 0.1）加入训练，提供额外监督信号。伪标签更新策略：
每训练N轮更新一次伪标签（避免噪声）。
仅保留长度≥MIN_SEQ_LEN（如 5）的序列以保证质量。
3.3 多任务损失融合
总损失由三部分组成：
监督损失（标注数据）：L_sup = BCEWithLogitsLoss(y_pred, s_true)。
对比损失（未标注数据）：L_cl = InfoNCE(z_pos, z_neg)。
伪标签损失（高置信未标注数据）：L_pseudo = BCEWithLogitsLoss(y_pred, s_pseudo)。
总损失：L_total = L_sup + α*L_cl + β*L_pseudo（α, β为超参数）。
4. 实现步骤与代码修改
4.1 数据加载与预处理
4.1.1 扩展KTData类支持未标注数据
修改data.py中的KTData类，增加对未标注数据的支持（s为-1表示未标注）：
python
运行
# DTransformer/data.py（修改后）
class KTData:
    def __init__(self, path, inputs, seq_len=None, batch_size=32, shuffle=False):
        # ...（原有代码）
        self.data = []
        with open(path, "r") as f:
            for line in f:
                parts = line.strip().split("\t")
                q = list(map(int, parts[0].split(",")))  # 问题ID序列
                s = list(map(int, parts[1].split(",")))  # 答题状态（-1表示未标注）
                self.data.append({"q": q, "s": s})
        # ...（其他逻辑）
4.1.2 划分标注 / 未标注数据集
在train.py中添加参数--unlabeled_ratio（如 0.3），从训练集中划分部分数据作为未标注集：
python
运行
# scripts/train.py（新增代码）
from sklearn.model_selection import train_test_split

# 加载原始训练数据
all_data = KTData(...)
# 划分标注/未标注数据
labeled_data, unlabeled_data = train_test_split(
    all_data, test_size=args.unlabeled_ratio, random_state=42
)
4.2 模型修改：支持伪标签生成
在DTransformer/model.py的predict方法中增加伪标签生成接口：
python
运行
# DTransformer/model.py（修改后）
class DTransformer(nn.Module):
    def predict_pseudo(self, q):
        """生成未标注数据的伪标签（s_pseudo）"""
        s_pseudo = torch.zeros_like(q)  # 初始化为0
        with torch.no_grad():
            # 假设未标注数据s全为-1，仅用q生成嵌入
            q_emb, _, lens, _ = self.embedding(q, s=torch.ones_like(q)*-1)
            z, _, _ = self(q_emb, q_emb, lens)  # 用q_emb替代s_emb（未标注）
            y = self.out(z).squeeze(-1)
            s_pseudo = torch.sigmoid(y)  # 概率值
        return s_pseudo
4.3 训练流程调整
修改train.py的训练循环，同时处理标注与未标注数据：
python
运行
# scripts/train.py（修改后）
def main(args):
    # ...（原有数据加载代码）
    
    # 加载标注与未标注数据迭代器
    labeled_iter = iter(labeled_data)
    unlabeled_iter = iter(unlabeled_data)

    for epoch in range(args.n_epochs):
        model.train()
        for batch in tqdm(labeled_iter):
            # ---------------------- 标注数据训练 ----------------------
            q_labeled, s_labeled = batch.get("q", "s")
            q_labeled = q_labeled.to(args.device)
            s_labeled = s_labeled.to(args.device)
            loss_sup = model.get_loss(q_labeled, s_labeled)  # 监督损失

            # ---------------------- 未标注数据训练 ----------------------
            try:
                batch_unlabeled = next(unlabeled_iter)
            except StopIteration:
                unlabeled_iter = iter(unlabeled_data)
                batch_unlabeled = next(unlabeled_iter)
            q_unlabeled = batch_unlabeled["q"].to(args.device)
            s_unlabeled = batch_unlabeled["s"].to(args.device)  # s全为-1

            # 生成伪标签（每N轮更新一次）
            if epoch % args.pseudo_update_freq == 0:
                s_pseudo = model.predict_pseudo(q_unlabeled)
                # 筛选高置信度样本（概率>0.9或<0.1）
                mask = (s_pseudo > 0.9) | (s_pseudo < 0.1)
                s_pseudo_filtered = s_pseudo[mask].round().long()
                q_pseudo_filtered = q_unlabeled[mask]

            # 计算对比损失（使用模型已有CL模块）
            _, _, cl_loss = model.get_cl_loss(q_unlabeled, s_unlabeled)  # s_unlabeled=-1时触发CL

            # 计算伪标签损失（仅高置信样本）
            if len(q_pseudo_filtered) > 0:
                loss_pseudo = F.binary_cross_entropy_with_logits(
                    model.predict(q_pseudo_filtered, s_pseudo_filtered)[0], 
                    s_pseudo_filtered.float()
                )
            else:
                loss_pseudo = 0.0

            # 总损失
            total_loss = loss_sup + args.alpha * cl_loss + args.beta * loss_pseudo
            total_loss.backward()
            optim.step()
            optim.zero_grad()

        # 验证与保存模型（原有逻辑）
        ...
4.4 超参数配置
在train.py中添加半监督相关超参数：
python
运行
# scripts/train.py（新增参数）
parser.add_argument("--unlabeled_ratio", type=float, default=0.3, help="未标注数据比例")
parser.add_argument("--alpha", type=float, default=0.1, help="对比损失权重")
parser.add_argument("--beta", type=float, default=0.05, help="伪标签损失权重")
parser.add_argument("--pseudo_update_freq", type=int, default=5, help="伪标签更新频率（轮次）")
5. 评估与验证
使用用户提供的eval.py中的Evaluator类，在验证集（全标注）上评估模型性能，关注指标：
AUC：反映模型对正负样本的区分能力。
ACC：伪标签筛选策略的有效性（高置信样本准确率应显著高于随机）。
RMSE：预测概率与真实标签的误差。
6. 注意事项
未标注数据质量：确保未标注数据与标注数据分布一致（如同类学生的答题序列）。
伪标签噪声：严格筛选高置信度样本（如设置概率阈值 > 0.95），避免噪声标签误导训练。
对比学习增强：根据数据集特性调整增强策略（如statics数据可增加知识点掩码，assist09可交换相邻答题记录）。
7. 总结
本方案通过对比学习增强未标注数据的表示能力，结合伪标签技术挖掘监督信号，在用户现有 DTransformer 模型基础上实现了半监督知识追踪。通过调整超参数（α, β、伪标签阈值）和增强策略，可适配不同数据集（如statics、assist09）的特性，提升模型在低标注场景下的性能。