"""
异常检测器训练策略基类

定义所有训练策略的通用接口和基础功能。
"""

import os
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from abc import ABC, abstractmethod
from typing import Dict, Optional, Tuple, Any
from collections import defaultdict

from ..generator import AnomalyGenerator
from ..evaluator import AnomalyEvaluator


class BaseDetectorStrategy(ABC):
    """异常检测器训练策略基类"""
    
    def __init__(self, model: nn.Module, device: str, save_dir: str, patience: int = 10):
        """
        初始化基础策略
        
        Args:
            model: 异常检测器模型
            device: 设备 (cuda/cpu)
            save_dir: 保存目录
            patience: 早停耐心值
        """
        self.model = model
        self.device = device
        self.save_dir = save_dir
        self.patience = patience
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 初始化组件
        self.generator = AnomalyGenerator()
        self.evaluator = AnomalyEvaluator()
        
        # 训练状态
        self.best_score = 0
        self.no_improve_count = 0
        self.training_history = defaultdict(list)
        
        # 策略特定的配置
        self.strategy_config = self._get_strategy_config()
        
    @abstractmethod
    def _get_strategy_config(self) -> Dict:
        """获取策略特定的配置"""
        pass
        
    @abstractmethod
    def _create_optimizer(self, learning_rate: float) -> torch.optim.Optimizer:
        """创建优化器"""
        pass
        
    @abstractmethod
    def _create_scheduler(self, optimizer: torch.optim.Optimizer, epochs: int) -> Optional[Any]:
        """创建学习率调度器"""
        pass
        
    @abstractmethod
    def _calculate_loss(self, outputs: torch.Tensor, targets: torch.Tensor, 
                       epoch: int, total_epochs: int) -> torch.Tensor:
        """计算损失"""
        pass
        
    @abstractmethod
    def _adjust_training_params(self, epoch: int, total_epochs: int, 
                               current_performance: Dict) -> Dict:
        """调整训练参数"""
        pass
        
    def train(self, train_loader, val_loader, epochs: int, learning_rate: float,
              anomaly_ratio: float = 0.1, optimize_for: str = 'f1_score', **kwargs) -> Dict:
        """
        执行训练
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            learning_rate: 学习率
            anomaly_ratio: 异常比例
            optimize_for: 优化目标
            **kwargs: 其他参数
            
        Returns:
            Dict: 训练结果
        """
        print(f"\n🚀 开始{self.get_strategy_name()}训练...")
        print(f"📊 策略配置: {self.strategy_config}")
        
        # 创建优化器和调度器
        optimizer = self._create_optimizer(learning_rate)
        scheduler = self._create_scheduler(optimizer, epochs)
        
        # 初始化训练状态
        self._reset_training_state()
        
        # 训练循环
        for epoch in range(epochs):
            # 调整训练参数
            current_performance = self._get_current_performance()
            adjusted_params = self._adjust_training_params(epoch, epochs, current_performance)
            
            # 训练一个epoch
            train_metrics = self._train_epoch(
                train_loader, optimizer, epoch, epochs, 
                anomaly_ratio, adjusted_params
            )
            
            # 验证
            val_metrics = self._validate_epoch(val_loader, epoch, anomaly_ratio)
            
            # 更新学习率
            if scheduler:
                if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_metrics[optimize_for])
                else:
                    scheduler.step()
            
            # 记录历史
            self._record_epoch_metrics(epoch, train_metrics, val_metrics)
            
            # 检查是否改进
            current_score = val_metrics[optimize_for]
            if self._is_better_score(current_score, optimize_for):
                self.best_score = current_score
                self.no_improve_count = 0
                self._save_best_model(epoch, val_metrics)
            else:
                self.no_improve_count += 1
                
            # 显示进度
            self._display_epoch_progress(epoch, epochs, train_metrics, val_metrics)
            
            # 早停检查
            if self.no_improve_count >= self.patience:
                print(f"\n⏹️  早停触发 (patience={self.patience})")
                break
                
        # 训练完成
        return self._finalize_training(optimize_for)
        
    def _train_epoch(self, train_loader, optimizer, epoch: int, total_epochs: int,
                    anomaly_ratio: float, adjusted_params: Dict) -> Dict:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, batch in enumerate(train_loader):
            q, s, pid = self._get_batch_data(batch)
            
            # 生成异常数据
            current_ratio = adjusted_params.get('anomaly_ratio', anomaly_ratio)
            s_anomaly, anomaly_labels = self.generator.generate_anomalies(
                q, s, pid, ratio=current_ratio
            )
            
            # 前向传播
            optimizer.zero_grad()
            outputs = self.model(q, s_anomaly, pid)
            
            # 计算损失
            loss = self._calculate_loss(outputs, anomaly_labels, epoch, total_epochs)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            if 'grad_clip' in adjusted_params:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), adjusted_params['grad_clip'])
                
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
        return {
            'loss': total_loss / num_batches,
            'anomaly_ratio': adjusted_params.get('anomaly_ratio', anomaly_ratio)
        }
        
    def _validate_epoch(self, val_loader, epoch: int, anomaly_ratio: float) -> Dict:
        """验证一个epoch"""
        self.model.eval()
        self.evaluator.reset()
        
        with torch.no_grad():
            for batch in val_loader:
                q, s, pid = self._get_batch_data(batch)
                
                # 生成异常数据
                s_anomaly, anomaly_labels = self.generator.generate_anomalies(
                    q, s, pid, ratio=anomaly_ratio
                )
                
                # 预测
                outputs = self.model(q, s_anomaly, pid)
                predictions = torch.sigmoid(outputs)
                
                # 评估
                self.evaluator.update(predictions, anomaly_labels)
                
        return self.evaluator.compute_metrics()
        
    def _get_batch_data(self, batch):
        """获取批次数据"""
        q = batch.get('q', batch.get('questions', batch[0])).to(self.device)
        s = batch.get('s', batch.get('responses', batch[1])).to(self.device)
        pid = batch.get('pid', batch.get('pids', batch[2])).to(self.device) if len(batch) > 2 else None
        return q, s, pid
        
    def _reset_training_state(self):
        """重置训练状态"""
        self.best_score = 0
        self.no_improve_count = 0
        self.training_history = defaultdict(list)
        
    def _get_current_performance(self) -> Dict:
        """获取当前性能"""
        if not self.training_history['val_f1_score']:
            return {}
        return {
            'f1_score': self.training_history['val_f1_score'][-1],
            'auc_roc': self.training_history['val_auc_roc'][-1],
            'precision': self.training_history['val_precision'][-1],
            'recall': self.training_history['val_recall'][-1]
        }
        
    def _record_epoch_metrics(self, epoch: int, train_metrics: Dict, val_metrics: Dict):
        """记录epoch指标"""
        self.training_history['epoch'].append(epoch)
        self.training_history['train_loss'].append(train_metrics['loss'])
        
        for key, value in val_metrics.items():
            self.training_history[f'val_{key}'].append(value)
            
    def _is_better_score(self, current_score: float, optimize_for: str) -> bool:
        """判断是否为更好的分数"""
        if optimize_for in ['loss', 'mae', 'rmse']:
            return current_score < self.best_score
        else:
            return current_score > self.best_score
            
    def _save_best_model(self, epoch: int, metrics: Dict):
        """保存最佳模型"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'metrics': metrics,
            'strategy': self.get_strategy_name()
        }
        
        torch.save(checkpoint, os.path.join(self.save_dir, 'best_model.pt'))
        
    def _display_epoch_progress(self, epoch: int, total_epochs: int, 
                               train_metrics: Dict, val_metrics: Dict):
        """显示epoch进度"""
        print(f"Epoch {epoch+1}/{total_epochs} - "
              f"Loss: {train_metrics['loss']:.4f}, "
              f"Val F1: {val_metrics.get('f1_score', 0):.4f}, "
              f"Val AUC: {val_metrics.get('auc_roc', 0):.4f}")
              
    def _finalize_training(self, optimize_for: str) -> Dict:
        """完成训练"""
        # 保存训练历史
        history_path = os.path.join(self.save_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(dict(self.training_history), f, indent=2)
            
        return {
            'best_score': self.best_score,
            'optimize_for': optimize_for,
            'strategy': self.get_strategy_name(),
            'training_history': dict(self.training_history)
        }
        
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass
        
    def get_strategy_info(self) -> Dict:
        """获取策略信息"""
        return {
            'name': self.get_strategy_name(),
            'config': self.strategy_config,
            'description': self.__class__.__doc__ or "No description available"
        }
