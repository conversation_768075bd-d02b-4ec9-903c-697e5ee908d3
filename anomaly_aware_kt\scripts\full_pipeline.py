#!/usr/bin/env python
"""
完整的异常感知知识追踪训练流程

包括：
1. 训练基线DTransformer模型
2. 训练异常检测器
3. 训练异常感知的知识追踪模型
4. 评估性能提升
"""

import os
import sys
import argparse
import torch
import tomlkit
import yaml
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DTransformer.data import KTData

# 导入重构后的流程管理器
from anomaly_kt.pipeline_stages import PipelineManager


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            config = yaml.safe_load(f)
        else:
            raise ValueError("Only YAML config files are supported")
    return config


def prepare_data(dataset_name: str, data_dir: str, batch_size: int, test_batch_size: int):
    """准备数据集"""
    # 加载数据集配置
    datasets = tomlkit.load(open(os.path.join(data_dir, 'datasets.toml')))
    dataset_config = datasets[dataset_name]

    # 创建数据加载器
    train_data = KTData(
        os.path.join(data_dir, dataset_config['train']),
        dataset_config['inputs'],
        batch_size=batch_size,
        shuffle=True
    )

    val_data = KTData(
        os.path.join(data_dir, dataset_config.get('valid', dataset_config['test'])),
        dataset_config['inputs'],
        batch_size=test_batch_size
    )

    test_data = KTData(
        os.path.join(data_dir, dataset_config['test']),
        dataset_config['inputs'],
        batch_size=test_batch_size
    )

    return train_data, val_data, test_data, dataset_config


def main():
    parser = argparse.ArgumentParser(description='Full Anomaly-Aware KT Pipeline')

    # 基本参数
    parser.add_argument('--dataset', required=True, choices=['assist09', 'assist17', 'algebra05', 'statics'])
    parser.add_argument('--data_dir', default='data')
    parser.add_argument('--output_dir', default=None)
    parser.add_argument('--device', default='cuda' if torch.cuda.is_available() else 'cpu')
    parser.add_argument('--config', help='Config file path')
    parser.add_argument('-p', '--with_pid', action='store_true')

    # 如果提供了配置文件，从配置文件加载参数
    args, _ = parser.parse_known_args()

    if args.config:
        config = load_config(args.config)
        parser.set_defaults(**config)

    # 数据参数
    parser.add_argument('--batch_size', type=int, default=32)
    parser.add_argument('--test_batch_size', type=int, default=64)

    # 基线模型参数
    parser.add_argument('--d_model', type=int, default=128)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--n_know', type=int, default=16)
    parser.add_argument('--n_layers', type=int, default=3)
    parser.add_argument('--dropout', type=float, default=0.2)
    parser.add_argument('--lambda_cl', type=float, default=0.1)
    parser.add_argument('--proj', action='store_true')
    parser.add_argument('--hard_neg', action='store_true')
    parser.add_argument('--window', type=int, default=1)

    # 异常检测器参数
    parser.add_argument('--detector_d_model', type=int, default=128)
    parser.add_argument('--detector_n_heads', type=int, default=8)
    parser.add_argument('--detector_n_layers', type=int, default=2)
    parser.add_argument('--detector_dropout', type=float, default=0.1)
    parser.add_argument('--window_size', type=int, default=10)
    parser.add_argument('--anomaly_ratio', type=float, default=0.1)
    parser.add_argument('--optimize_for', default='f1_score',
                       choices=['f1_score', 'auc_roc', 'recall', 'precision', 'balanced_accuracy', 'mcc'],
                       help='Optimization target: f1_score (balanced), recall (find all anomalies), precision (avoid false alarms), auc_roc (overall performance)')

    # 训练参数
    parser.add_argument('--kt_epochs', type=int, default=100)
    parser.add_argument('--detector_epochs', type=int, default=30)
    parser.add_argument('--learning_rate', type=float, default=1e-3)
    parser.add_argument('--detector_lr', type=float, default=1e-3)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--detector_patience', type=int, default=10)
    parser.add_argument('--use_cl', action='store_true')

    # 异常感知参数
    parser.add_argument('--anomaly_weight', type=float, default=0.5)
    parser.add_argument('--use_enhanced_anomaly_aware', action='store_true',help='Use enhanced anomaly-aware training')
    parser.add_argument('--use_aa_curriculum', action='store_true',help='Use curriculum learning for anomaly-aware training')
    parser.add_argument('--use_aa_game_theory', action='store_true',help='Use game theory for anomaly-aware training')
    # 控制参数
    parser.add_argument('--skip_baseline', action='store_true', help='Skip baseline training')
    parser.add_argument('--skip_detector', action='store_true', help='Skip detector training')
    parser.add_argument('--baseline_path', help='Path to existing baseline model')
    parser.add_argument('--detector_path', help='Path to existing detector model')

    # 训练策略参数
    parser.add_argument('--training_strategy', default='basic',
                       choices=['basic', 'enhanced', 'aggressive'],
                       help='Training strategy for anomaly detector: basic (default), enhanced, or aggressive')
    parser.add_argument('--use_curriculum', action='store_true',
                       help='Enable curriculum learning for enhanced training strategy')

    # 向后兼容的参数
    parser.add_argument('--use_aggressive_strategy', action='store_true',
                       help='Use aggressive training strategy (deprecated, use --training_strategy aggressive)')

    args = parser.parse_args()

    # 向后兼容性处理
    if args.use_aggressive_strategy:
        args.training_strategy = 'aggressive'
        print("⚠️  注意: --use_aggressive_strategy 已弃用，请使用 --training_strategy aggressive")

    # 检查基线模型文件是否存在
    if args.skip_baseline:
        if not args.baseline_path:
            print("ERROR: --skip_baseline requires --baseline_path to be specified")
            sys.exit(1)

        if not os.path.exists(args.baseline_path):
            print(f"ERROR: Baseline model file not found: {args.baseline_path}")
            print(f"Please check the path or remove --skip_baseline to train a new baseline model")
            sys.exit(1)
        else:
            print(f"✓ Baseline model found: {args.baseline_path}")

    # 检查异常检测器文件是否存在（如果指定了）
    if args.skip_detector:
        if not args.detector_path:
            print("ERROR: --skip_detector requires --detector_path to be specified")
            sys.exit(1)

        if not os.path.exists(args.detector_path):
            print(f"ERROR: Detector model file not found: {args.detector_path}")
            print(f"Please check the path or remove --skip_detector to train a new detector")
            sys.exit(1)
        else:
            print(f"✓ Detector model found: {args.detector_path}")

    # 设置输出目录
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output_dir = f"output/{args.dataset}_{timestamp}"

    os.makedirs(args.output_dir, exist_ok=True)

    # 保存配置
    config_save_path = os.path.join(args.output_dir, 'config.yaml')
    with open(config_save_path, 'w') as f:
        yaml.dump(vars(args), f, default_flow_style=False)

    print("Configuration:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")

    # 准备数据
    train_data, val_data, test_data, dataset_config = prepare_data(
        args.dataset, args.data_dir, args.batch_size, args.test_batch_size
    )

    # 创建流程管理器并运行完整流程
    pipeline = PipelineManager(args)
    results = pipeline.run_pipeline(train_data, val_data, test_data, dataset_config)


if __name__ == '__main__':
    main()