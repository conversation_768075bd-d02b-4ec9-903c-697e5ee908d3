#!/usr/bin/env python3
"""
断点续训脚本 - 从指定检查点继续训练异常检测器
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import argparse
from datetime import datetime
from tqdm import tqdm
import json
import tomlkit

# 添加项目路径
sys.path.append('.')
sys.path.append('anomaly_aware_kt')

from DTransformer.data import KTData
from anomaly_kt.detector import CausalAnomalyDetector
from anomaly_kt.generator import AnomalyGenerator
from anomaly_kt.evaluator import AnomalyEvaluator


class ResumeTrainer:
    def __init__(self, model, device, save_dir, patience=10):
        self.model = model.to(device)
        self.device = device
        self.save_dir = save_dir
        self.patience = patience
        self.generator = AnomalyGenerator()
        self.evaluator = AnomalyEvaluator()
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 训练历史
        self.history = {
            'train_loss': [],
            'val_recall': [],
            'val_precision': [],
            'val_f1': [],
            'val_auc': []
        }
    
    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        print(f"📥 加载检查点: {checkpoint_path}")
        
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 加载模型状态
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 获取训练信息
        start_epoch = checkpoint.get('epoch', 0)
        saved_metrics = checkpoint.get('metrics', {})
        
        print(f"🔢 从第 {start_epoch} 轮继续训练")
        print(f"📊 上次性能:")
        for key, value in saved_metrics.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value:.4f}")
        
        return start_epoch, checkpoint.get('optimizer_state_dict', None)
    
    def save_checkpoint(self, epoch, optimizer, metrics, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch + 1,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'metrics': metrics,
            'history': self.history
        }
        
        # 保存当前轮次
        epoch_path = os.path.join(self.save_dir, f'model_epoch_{epoch+1}.pt')
        torch.save(checkpoint, epoch_path)
        print(f"  💾 保存: epoch_{epoch+1}.pt")
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.save_dir, 'best_model.pt')
            torch.save(checkpoint, best_path)
            print(f"  🏆 保存最佳模型")
    
    def train_epoch(self, train_loader, optimizer, anomaly_ratio=0.3):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        n_batches = 0
        
        # 统计信息
        pos_count = 0
        neg_count = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            q, s, pid = self._get_batch_data(batch)
            
            # 生成异常数据
            s_anomaly, labels = self.generator.generate_balanced_anomalies(
                q, s, anomaly_ratio
            )
            
            # 前向传播
            logits = self.model(q, s_anomaly, pid)
            
            # 计算损失
            valid_mask = labels >= 0
            if valid_mask.sum() == 0:
                continue
                
            loss = nn.BCEWithLogitsLoss()(
                logits[valid_mask], 
                labels[valid_mask].float()
            )
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            optimizer.step()
            
            total_loss += loss.item()
            n_batches += 1
            
            # 统计正负样本
            pos_count += (labels[valid_mask] == 1).sum().item()
            neg_count += (labels[valid_mask] == 0).sum().item()
        
        avg_loss = total_loss / n_batches if n_batches > 0 else 0
        
        print(f"  训练损失: {avg_loss:.4f}")
        print(f"  样本统计 - 正样本: {pos_count}, 负样本: {neg_count}")
        
        return avg_loss
    
    def validate(self, val_loader, anomaly_ratio=0.3):
        """验证模型"""
        self.model.eval()
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                q, s, pid = self._get_batch_data(batch)
                
                # 生成异常数据
                s_anomaly, labels = self.generator.generate_balanced_anomalies(
                    q, s, anomaly_ratio
                )
                
                # 预测
                logits = self.model(q, s_anomaly, pid)
                preds = torch.sigmoid(logits)
                
                # 收集有效预测
                valid_mask = labels >= 0
                if valid_mask.sum() > 0:
                    all_preds.extend(preds[valid_mask].cpu().numpy())
                    all_labels.extend(labels[valid_mask].cpu().numpy())
        
        # 计算指标
        if len(all_preds) > 0:
            metrics = self.evaluator.evaluate_predictions(all_preds, all_labels)
        else:
            metrics = {'recall': 0, 'precision': 0, 'f1_score': 0, 'auc_roc': 0}
        
        return metrics
    
    def _get_batch_data(self, batch):
        """获取批次数据"""
        if hasattr(batch, 'get'):
            q, s, pid = batch.get("q", "s", "pid")
        else:
            q, s, pid = batch["q"], batch["s"], batch.get("pid", None)
        
        # 处理列表格式
        if isinstance(q, list):
            q, s = q[0], s[0]
            pid = pid[0] if pid is not None else None
        
        # 转移到设备
        q = q.to(self.device)
        s = s.to(self.device)
        if pid is not None:
            pid = pid.to(self.device)
        
        return q, s, pid
    
    def train(self, train_loader, val_loader, start_epoch, total_epochs, 
              learning_rate=1e-3, optimize_for='recall', optimizer_state=None):
        """完整训练流程"""
        
        # 创建优化器
        optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=learning_rate,
            weight_decay=1e-5
        )
        
        # 恢复优化器状态
        if optimizer_state is not None:
            try:
                optimizer.load_state_dict(optimizer_state)
                print("✅ 优化器状态已恢复")
            except:
                print("⚠️  优化器状态恢复失败，使用新的优化器")
        
        # 学习率调度器
        scheduler = ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=5
        )
        
        best_score = 0
        no_improve = 0
        
        print(f"\n🚀 开始训练 (从第{start_epoch}轮到第{total_epochs}轮)")
        
        for epoch in range(start_epoch, total_epochs):
            print(f"\n=== Epoch {epoch+1}/{total_epochs} ===")
            
            # 训练
            train_loss = self.train_epoch(train_loader, optimizer)
            
            # 验证
            val_metrics = self.validate(val_loader)
            
            # 更新学习率
            scheduler.step(val_metrics[optimize_for])
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_recall'].append(val_metrics['recall'])
            self.history['val_precision'].append(val_metrics['precision'])
            self.history['val_f1'].append(val_metrics['f1_score'])
            self.history['val_auc'].append(val_metrics['auc_roc'])
            
            # 打印结果
            print(f"验证结果:")
            print(f"  Recall: {val_metrics['recall']:.4f}")
            print(f"  Precision: {val_metrics['precision']:.4f}")
            print(f"  F1: {val_metrics['f1_score']:.4f}")
            print(f"  AUC: {val_metrics['auc_roc']:.4f}")
            
            # 检查最佳模型
            current_score = val_metrics[optimize_for]
            is_best = current_score > best_score
            
            if is_best:
                best_score = current_score
                no_improve = 0
                print(f"  ✅ 新的最佳{optimize_for}: {current_score:.4f}")
            else:
                no_improve += 1
                print(f"  📊 当前{optimize_for}: {current_score:.4f} (最佳: {best_score:.4f})")
            
            # 保存检查点
            self.save_checkpoint(epoch, optimizer, val_metrics, is_best)
            
            # 早停检查
            if no_improve >= self.patience:
                print(f"\n⏹️  早停触发 - {self.patience}轮无改善")
                break
        
        print(f"\n🎉 训练完成! 最佳{optimize_for}: {best_score:.4f}")
        return {'best_score': best_score, 'optimize_for': optimize_for}


def main():
    parser = argparse.ArgumentParser(description='断点续训异常检测器')
    
    # 必需参数
    parser.add_argument('--checkpoint', required=True, help='检查点文件路径')
    parser.add_argument('--dataset', default='assist17', help='数据集名称')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=50, help='总训练轮数')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--test_batch_size', type=int, default=32, help='测试批次大小')
    parser.add_argument('--anomaly_ratio', type=float, default=0.3, help='异常比例')
    parser.add_argument('--optimize_for', default='recall', help='优化目标')
    
    # 模型参数
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--n_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--n_layers', type=int, default=2, help='层数')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout率')
    parser.add_argument('--window_size', type=int, default=10, help='窗口大小')
    
    # 其他参数
    parser.add_argument('--device', default='cuda', help='设备')
    parser.add_argument('--with_pid', action='store_true', help='使用问题ID')
    parser.add_argument('--data_dir', default='data', help='数据目录')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心')
    
    args = parser.parse_args()
    
    print("🔄 断点续训异常检测器")
    print("="*50)
    print(f"检查点: {args.checkpoint}")
    print(f"数据集: {args.dataset}")
    print(f"目标轮数: {args.epochs}")
    print(f"设备: {args.device}")
    
    # 检查检查点文件
    if not os.path.exists(args.checkpoint):
        print(f"❌ 检查点文件不存在: {args.checkpoint}")
        return
    
    # 加载数据集配置
    datasets = tomlkit.load(open(os.path.join(args.data_dir, "datasets.toml")))
    dataset_config = datasets[args.dataset]
    
    # 准备数据
    print("\n📊 准备数据...")
    train_data = KTData(
        os.path.join(args.data_dir, dataset_config["train_file"]),
        batch_size=args.batch_size,
        shuffle=True,
        device=args.device
    )
    
    val_data = KTData(
        os.path.join(args.data_dir, dataset_config["val_file"]),
        batch_size=args.test_batch_size,
        shuffle=False,
        device=args.device
    )
    
    print(f"训练批次: {len(train_data)}, 验证批次: {len(val_data)}")
    
    # 创建模型
    print("\n🧠 创建模型...")
    model = CausalAnomalyDetector(
        n_questions=dataset_config['n_questions'],
        n_pid=dataset_config['n_pid'] if args.with_pid else 0,
        d_model=args.d_model,
        n_heads=args.n_heads,
        n_layers=args.n_layers,
        dropout=args.dropout,
        window_size=args.window_size
    )
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"output/resume_{args.dataset}_{timestamp}/detector"
    
    # 创建训练器
    trainer = ResumeTrainer(model, args.device, save_dir, args.patience)
    
    # 加载检查点
    start_epoch, optimizer_state = trainer.load_checkpoint(args.checkpoint)
    
    # 开始训练
    try:
        results = trainer.train(
            train_data, val_data, start_epoch, args.epochs,
            args.learning_rate, args.optimize_for, optimizer_state
        )
        
        print(f"\n✅ 训练成功完成!")
        print(f"📁 模型保存在: {save_dir}")
        
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        print("💾 所有模型已保存，可以随时继续")
    except Exception as e:
        print(f"\n❌ 训练出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
