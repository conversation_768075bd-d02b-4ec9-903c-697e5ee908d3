"""
第1阶段：基线DTransformer模型训练

训练标准的知识追踪模型，作为后续对比的基准。
这个阶段不涉及任何异常检测功能，专注于建立高质量的基线性能。
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Any
from DTransformer.data import KTData
from DTransformer.model import DTransformer
from ..trainer import KnowledgeTracingTrainer


class BaselineTrainingStage:
    """第1阶段：基线DTransformer模型训练"""
    
    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化基线训练阶段
        
        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir
        self.save_dir = os.path.join(output_dir, 'baseline')
        os.makedirs(self.save_dir, exist_ok=True)
        
    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData) -> str:
        """
        执行基线模型训练
        
        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据
            
        Returns:
            str: 保存的模型路径
        """
        print("\n" + "="*60)
        print("STAGE 1: Training Baseline DTransformer Model")
        print("="*60)
        print("🎯 目标: 建立高质量的基线知识追踪模型")
        print("📊 特点: 标准DTransformer，无异常检测功能")
        
        # 创建基线模型
        model = self._create_baseline_model(dataset_config)
        print(f"🧠 基线模型参数: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建训练器
        trainer = self._create_trainer(model)
        
        # 执行训练
        baseline_metrics = self._train_model(trainer, train_data, val_data)
        
        # 保存结果
        model_path = self._save_results(baseline_metrics)
        
        print(f"\n✅ Stage 1 完成!")
        print(f"📈 最佳 AUC: {baseline_metrics['auc']:.4f}")
        print(f"💾 模型保存至: {model_path}")
        
        return model_path
        
    def _create_baseline_model(self, dataset_config: Dict) -> nn.Module:
        """创建基线DTransformer模型"""
        model = DTransformer(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=self.args.d_model,
            n_heads=self.args.n_heads,
            n_know=self.args.n_know,
            n_layers=self.args.n_layers,
            dropout=self.args.dropout,
            lambda_cl=self.args.lambda_cl,
            proj=self.args.proj,
            hard_neg=self.args.hard_neg,
            window=self.args.window
        )
        return model.to(self.device)
        
    def _create_trainer(self, model: nn.Module) -> KnowledgeTracingTrainer:
        """创建训练器"""
        return KnowledgeTracingTrainer(
            model=model,
            device=self.device,
            save_dir=self.save_dir,
            patience=self.args.patience
        )
        
    def _train_model(self, trainer: KnowledgeTracingTrainer, 
                    train_data: KTData, val_data: KTData) -> Dict:
        """执行模型训练"""
        print(f"\n🚀 开始基线模型训练...")
        print(f"📊 训练轮数: {self.args.kt_epochs}")
        print(f"📈 学习率: {self.args.learning_rate}")
        print(f"🎯 使用课程学习: {self.args.use_cl}")
        
        return trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=self.args.kt_epochs,
            learning_rate=self.args.learning_rate,
            use_cl=self.args.use_cl
        )
        
    def _save_results(self, metrics: Dict) -> str:
        """保存训练结果"""
        # 保存训练指标
        import json
        metrics_path = os.path.join(self.save_dir, 'training_metrics.json')
        with open(metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)
            
        # 返回最佳模型路径
        return os.path.join(self.save_dir, 'best_model.pt')
        
    def get_stage_info(self) -> Dict:
        """获取阶段信息"""
        return {
            'stage_number': 1,
            'stage_name': 'baseline_training',
            'description': '基线DTransformer模型训练',
            'purpose': '建立高质量的基线知识追踪模型',
            'output': '训练好的基线模型文件',
            'dependencies': [],
            'next_stage': 'detector_training'
        }
