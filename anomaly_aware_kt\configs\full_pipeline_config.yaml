# full_pipeline.py 嵌套结构配置文件
# 支持全局配置、数据集特定配置和阶段特定配置

# ==================== 全局配置 ====================
global:
  # 基本设置
  data_dir: "data"
  output_dir: null  # null表示自动生成时间戳目录
  device: "auto"  # auto, cuda, cpu
  random_seed: 42

  # 通用数据参数
  data:
    with_pid: true
    batch_size: 32
    test_batch_size: 64

  # 通用训练参数
  training:
    learning_rate: 1e-3
    patience: 15
    use_cl: true

  # 控制参数
  control:
    skip_baseline: false
    skip_detector: false
    skip_anomaly_training: false
    show_pipeline_info: false
    show_strategies: false

# ==================== 阶段特定配置 ====================
stages:
  # 第1阶段：基线模型训练
  stage1_baseline:
    name: "Baseline DTransformer Training"

    # 模型架构
    model:
      d_model: 128
      n_heads: 8
      n_know: 32
      n_layers: 3
      dropout: 0.2
      lambda_cl: 0.1
      proj: true
      hard_neg: false
      window: 1

    # 训练参数
    training:
      epochs: 100  # kt_epochs
      learning_rate: 1e-3
      patience: 15
      use_cl: true

    # 数据参数
    data:
      batch_size: null  # 继承全局设置
      test_batch_size: null

  # 第2阶段：异常检测器训练
  stage2_detector:
    name: "Anomaly Detector Training"

    # 检测器架构
    model:
      d_model: 128  # detector_d_model
      n_heads: 8    # detector_n_heads
      n_layers: 2   # detector_n_layers
      dropout: 0.1  # detector_dropout
      window_size: 10

    # 训练参数
    training:
      epochs: 50    # detector_epochs
      learning_rate: 1e-3  # detector_lr
      patience: 15  # detector_patience
      strategy: "enhanced"  # training_strategy
      use_curriculum: true

    # 异常检测配置
    anomaly:
      ratio: 0.15
      optimize_for: "f1_score"
      types: ["consecutive", "difficulty_based", "pattern", "random_burst"]

    # 数据参数
    data:
      batch_size: null  # 继承全局设置
      test_batch_size: null

  # 第3阶段：异常感知训练
  stage3_anomaly_aware:
    name: "Anomaly-Aware Knowledge Tracing Training"

    # 异常感知参数
    anomaly_aware:
      weight: 0.1  # anomaly_weight
      use_enhanced: true  # use_enhanced_anomaly_aware
      use_curriculum: true  # use_aa_curriculum
      use_game_theory: true  # use_aa_game_theory

    # 训练参数
    training:
      epochs: 100  # kt_epochs
      learning_rate: 1e-3
      patience: 15
      use_cl: true

    # 数据参数
    data:
      batch_size: null  # 继承全局设置
      test_batch_size: null

    # 检查点恢复
    resume:
      from_checkpoint: null  # resume_from_checkpoint
      start_epoch: 1

  # 第4阶段：评估与对比
  stage4_evaluation:
    name: "Model Evaluation and Comparison"

    # 评估配置
    evaluation:
      metrics: ["accuracy", "auc", "mae", "rmse"]
      target_improvement: 1.0  # 目标AUC提升百分比

    # 模型路径
    models:
      baseline_path: null
      anomaly_aware_path: null
      detector_path: null

# ==================== 数据集特定配置 ====================
datasets:
  assist09:
    name: "ASSISTments 2009-2010"
    description: "较小规模数据集，适合快速实验"

    # 全局覆盖
    global:
      data:
        batch_size: 16
        with_pid: true

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          n_know: 16
          d_model: 64
        training:
          epochs: 80

      stage2_detector:
        model:
          d_model: 64
        training:
          strategy: "basic"
          epochs: 30
        anomaly:
          ratio: 0.12

      stage3_anomaly_aware:
        anomaly_aware:
          weight: 0.12
        training:
          epochs: 80

  assist17:
    name: "ASSISTments 2017"
    description: "中等规模数据集，推荐用于大多数实验"

    # 全局覆盖
    global:
      data:
        batch_size: 32
        with_pid: true

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          n_know: 32
          d_model: 128
        training:
          epochs: 100

      stage2_detector:
        model:
          d_model: 128
        training:
          strategy: "enhanced"
          epochs: 50
        anomaly:
          ratio: 0.15

      stage3_anomaly_aware:
        anomaly_aware:
          weight: 0.08
        training:
          epochs: 100

  algebra05:
    name: "Algebra 2005-2006"
    description: "大规模数据集，计算要求较高"

    # 全局覆盖
    global:
      data:
        batch_size: 64
        with_pid: false  # 减少计算复杂度

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          n_know: 64
          d_model: 128
        training:
          epochs: 120

      stage2_detector:
        model:
          d_model: 128
        training:
          strategy: "basic"
          epochs: 40
        anomaly:
          ratio: 0.08

      stage3_anomaly_aware:
        anomaly_aware:
          weight: 0.06
        training:
          epochs: 120

  statics:
    name: "Statics"
    description: "特殊数据集，异常行为较多"

    # 全局覆盖
    global:
      data:
        batch_size: 16
        with_pid: true

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          n_know: 32
          d_model: 128
        training:
          epochs: 100

      stage2_detector:
        model:
          d_model: 128
        training:
          strategy: "aggressive"
          epochs: 60
        anomaly:
          ratio: 0.25

      stage3_anomaly_aware:
        anomaly_aware:
          weight: 0.15
        training:
          epochs: 100

# ==================== 预设配置模板 ====================
presets:
  quick_test:
    name: "快速测试配置"
    description: "用于快速验证和调试"

    # 全局覆盖
    global:
      data:
        batch_size: 64
        test_batch_size: 128
      training:
        learning_rate: 2e-3
        patience: 5

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          d_model: 64
          n_layers: 2
        training:
          epochs: 20

      stage2_detector:
        model:
          d_model: 64
          n_layers: 1
        training:
          epochs: 15
          strategy: "basic"

      stage3_anomaly_aware:
        training:
          epochs: 20
        anomaly_aware:
          use_enhanced: false
          use_curriculum: false
          use_game_theory: false

  high_performance:
    name: "高性能配置"
    description: "追求最佳性能的配置"

    # 全局覆盖
    global:
      data:
        batch_size: 16
        test_batch_size: 32
      training:
        learning_rate: 5e-4
        patience: 25

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          d_model: 256
          n_heads: 16
          n_layers: 6
        training:
          epochs: 150

      stage2_detector:
        model:
          d_model: 256
          n_heads: 16
          n_layers: 4
        training:
          epochs: 80
          strategy: "enhanced"

      stage3_anomaly_aware:
        training:
          epochs: 150
        anomaly_aware:
          weight: 0.05

  resource_limited:
    name: "资源受限配置"
    description: "适用于计算资源有限的环境"

    # 全局覆盖
    global:
      data:
        batch_size: 128
        test_batch_size: 256
        with_pid: false
      training:
        learning_rate: 2e-3
        patience: 8

    # 阶段覆盖
    stages:
      stage1_baseline:
        model:
          d_model: 64
          n_heads: 4
          n_layers: 2
          proj: false
        training:
          epochs: 50
          use_cl: false

      stage2_detector:
        model:
          d_model: 64
          n_heads: 4
          n_layers: 1
        training:
          epochs: 25
          strategy: "basic"
        anomaly:
          ratio: 0.08

      stage3_anomaly_aware:
        training:
          epochs: 50
        anomaly_aware:
          weight: 0.2
          use_enhanced: false
          use_curriculum: false
          use_game_theory: false

# ==================== 使用说明 ====================
# 配置文件层次结构：
# 1. global: 全局配置，影响所有阶段
# 2. stages: 阶段特定配置，覆盖全局配置
# 3. datasets: 数据集特定配置，覆盖全局和阶段配置
# 4. presets: 预设配置模板，可以快速应用
# 5. 命令行参数: 最高优先级，覆盖所有配置文件设置
#
# 使用方法：
# 1. 基本使用（自动应用数据集特定配置）：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml
#
# 2. 使用预设配置：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml --preset quick_test
#
# 3. 覆盖特定参数：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml --batch_size 16
#
# 4. 跳过阶段：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml \
#        --skip_baseline --baseline_path output/baseline/best_model.pt
#
# 5. 恢复训练：
#    python full_pipeline.py --dataset assist17 --config configs/full_pipeline_config.yaml \
#        --resume_from_checkpoint output/epoch_9.pt --start_epoch 10
#
# 配置优先级（从高到低）：
# 命令行参数 > 预设配置 > 数据集配置 > 阶段配置 > 全局配置
