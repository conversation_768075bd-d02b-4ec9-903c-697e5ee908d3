"""
数据集角色演示 - 展示数据集在博弈论框架中的多重角色

本示例展示了数据集如何在博弈论异常检测框架中扮演：
1. 博弈环境的基础设施
2. 教师的知识来源
3. 学生-对手博弈的战场
4. 动态难度调节的智能顾问
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any
import time

# 导入框架组件
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from anomaly_kt.game_theory.dataset_manager import GameDatasetManager, DataSample
from anomaly_kt.game_theory.game_environment import GameEnvironment
from anomaly_kt.game_theory.game_agents import TeacherAgent, StudentAgent, AdversaryAgent, GameState


def create_demo_dataset(n_samples: int = 1000) -> List[DataSample]:
    """创建演示数据集"""
    print("🔧 创建演示数据集...")
    
    samples = []
    
    for i in range(n_samples):
        # 生成特征
        if i < n_samples * 0.8:  # 80% 正常样本
            features = torch.normal(0, 1, (10,))
            label = 0
        else:  # 20% 异常样本
            features = torch.normal(2, 1.5, (10,))  # 异常样本有不同分布
            label = 1
            
        # 计算基础难度
        feature_complexity = torch.std(features).item()
        difficulty = min(1.0, feature_complexity / 2.0)
        
        # 确定领域
        if i < n_samples * 0.4:
            domain = "knowledge_tracing"
        elif i < n_samples * 0.7:
            domain = "cybersecurity"
        else:
            domain = "financial"
            
        sample = DataSample(
            features=features,
            label=label,
            difficulty=difficulty,
            domain=domain,
            metadata={'id': i, 'created_time': time.time()}
        )
        
        samples.append(sample)
        
    print(f"✅ 创建了 {len(samples)} 个样本")
    return samples


def demonstrate_role_1_environment_infrastructure():
    """演示角色1：博弈环境的基础设施"""
    print("\n" + "="*60)
    print("🏗️ 角色1演示：博弈环境的基础设施")
    print("="*60)
    
    # 创建数据集管理器
    dataset_config = {
        'enable_dynamic_difficulty': True,
        'curriculum_stages': ['foundation', 'adversarial', 'mastery']
    }
    
    dataset_manager = GameDatasetManager(dataset_config)
    
    # 加载演示数据
    demo_samples = create_demo_dataset(500)
    dataset_manager.raw_dataset = demo_samples
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    print("\n📊 数据集分区信息:")
    for stage, partition in dataset_manager.partitions.items():
        print(f"   {stage}: {len(partition.samples)} 样本, "
              f"难度范围: {partition.difficulty_range}, "
              f"异常比例: {partition.anomaly_ratio:.2%}")
    
    # 模拟不同博弈状态下的环境提供
    game_states = [
        GameState(0, {}, 0.3, 'foundation', 0.3, []),
        GameState(1, {}, 0.6, 'adversarial', 0.6, []),
        GameState(2, {}, 0.8, 'mastery', 0.9, [])
    ]
    
    print("\n🎮 不同博弈状态下的环境响应:")
    for i, state in enumerate(game_states):
        env_state = dataset_manager.get_game_environment_state(state)
        
        if env_state['sample']:
            sample = env_state['sample']
            env_info = env_state['environment_state']
            
            print(f"\n   状态 {i+1} ({state.curriculum_stage}):")
            print(f"     选择样本ID: {sample.metadata['id']}")
            print(f"     样本难度: {sample.difficulty:.3f}")
            print(f"     样本领域: {sample.domain}")
            print(f"     可用样本数: {env_info['available_samples']}")
            print(f"     分区信息: {env_info['partition_info']['total_samples']} 总样本")


def demonstrate_role_2_teacher_knowledge_source():
    """演示角色2：教师的知识来源"""
    print("\n" + "="*60)
    print("🎓 角色2演示：教师的知识来源")
    print("="*60)
    
    # 使用之前创建的数据集管理器
    dataset_config = {'enable_dynamic_difficulty': True}
    dataset_manager = GameDatasetManager(dataset_config)
    dataset_manager.raw_dataset = create_demo_dataset(300)
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    # 演示不同类型的知识查询
    queries = [
        {'type': 'difficulty_distribution'},
        {'type': 'student_performance_prediction', 'student_history': [0.6, 0.7, 0.8, 0.75]},
        {'type': 'curriculum_recommendation', 'current_stage': 'foundation', 
         'student_performance': 0.8, 'adversary_performance': 0.4}
    ]
    
    print("\n🧠 教师知识查询结果:")
    
    for i, query in enumerate(queries):
        print(f"\n   查询 {i+1}: {query['type']}")
        knowledge = dataset_manager.provide_teacher_knowledge(query)
        
        if query['type'] == 'difficulty_distribution':
            print(f"     平均难度: {knowledge['mean_difficulty']:.3f}")
            print(f"     难度标准差: {knowledge['std_difficulty']:.3f}")
            print(f"     各阶段分布: {knowledge['stage_distributions']}")
            
        elif query['type'] == 'student_performance_prediction':
            print(f"     预测准确率: {knowledge['predicted_accuracy']:.3f}")
            print(f"     预测置信度: {knowledge['confidence']:.3f}")
            print(f"     推荐样本数: {len(knowledge['recommended_samples'])}")
            
        elif query['type'] == 'curriculum_recommendation':
            print(f"     推荐行动: {knowledge['recommendation']}")
            print(f"     目标阶段: {knowledge['target_stage']}")
            print(f"     推荐理由: {knowledge['reasoning']}")


def demonstrate_role_3_battleground():
    """演示角色3：学生-对手博弈的战场"""
    print("\n" + "="*60)
    print("⚔️ 角色3演示：学生-对手博弈的战场")
    print("="*60)
    
    # 创建数据集管理器和博弈环境
    dataset_config = {'enable_dynamic_difficulty': True}
    dataset_manager = GameDatasetManager(dataset_config)
    dataset_manager.raw_dataset = create_demo_dataset(200)
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    # 模拟智能体策略
    student_strategy = {
        'detection_threshold': 0.6,
        'feature_weights': torch.ones(10),
        'confidence_level': 0.8
    }
    
    adversary_strategy = {
        'anomaly_type': 'stealth',
        'intensity': 0.7,
        'target_features': [0, 2, 5],
        'evasion_technique': 'gradual'
    }
    
    teacher_strategy = {
        'intervention': {'type': 'hint_provision', 'intensity': 0.5},
        'difficulty_level': 0.6
    }
    
    print("\n🎯 创建博弈战场:")
    
    # 创建战场
    battleground = dataset_manager.create_battleground(
        student_strategy, adversary_strategy, teacher_strategy
    )
    
    if 'error' not in battleground:
        original_sample = battleground['original_sample']
        student_view = battleground['student_view']
        adversary_view = battleground['adversary_view']
        
        print(f"\n   原始样本信息:")
        print(f"     样本ID: {original_sample.metadata['id']}")
        print(f"     真实标签: {original_sample.label}")
        print(f"     样本难度: {original_sample.difficulty:.3f}")
        print(f"     样本领域: {original_sample.domain}")
        
        print(f"\n   学生视角:")
        print(f"     可见特征维度: {len(student_view['features'])}")
        print(f"     检测阈值: {student_view['detection_context']['threshold']}")
        print(f"     知道真实标签: {student_view['available_info']['has_true_label']}")
        print(f"     知道真实难度: {student_view['available_info']['has_difficulty']}")
        
        print(f"\n   对手视角:")
        print(f"     目标特征维度: {len(adversary_view['original_features'])}")
        print(f"     攻击类型: {adversary_view['generation_context']['anomaly_type']}")
        print(f"     攻击强度: {adversary_view['generation_context']['intensity']}")
        print(f"     知道真实标签: {adversary_view['available_info']['has_true_label']}")
        print(f"     知道学生策略: {adversary_view['available_info']['has_difficulty']}")
    else:
        print(f"   ❌ 战场创建失败: {battleground['error']}")


def demonstrate_role_4_dynamic_difficulty_advisor():
    """演示角色4：动态难度调节的智能顾问"""
    print("\n" + "="*60)
    print("📈 角色4演示：动态难度调节的智能顾问")
    print("="*60)
    
    # 创建数据集管理器
    dataset_config = {'enable_dynamic_difficulty': True}
    dataset_manager = GameDatasetManager(dataset_config)
    dataset_manager.raw_dataset = create_demo_dataset(400)
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    # 模拟不同的博弈状态和表现历史
    scenarios = [
        {
            'name': '学生表现优秀',
            'game_state': GameState(10, {'detection_accuracy': 0.9}, 0.2, 'foundation', 0.4, []),
            'performance_history': [0.85, 0.88, 0.90, 0.92, 0.89]
        },
        {
            'name': '对手过于强势',
            'game_state': GameState(20, {'detection_accuracy': 0.3}, 0.8, 'adversarial', 0.7, []),
            'performance_history': [0.6, 0.5, 0.4, 0.3, 0.25]
        },
        {
            'name': '博弈相对平衡',
            'game_state': GameState(30, {'detection_accuracy': 0.7}, 0.6, 'adversarial', 0.6, []),
            'performance_history': [0.65, 0.70, 0.68, 0.72, 0.69]
        }
    ]
    
    print("\n🎯 不同场景下的难度调节建议:")
    
    for scenario in scenarios:
        print(f"\n   场景: {scenario['name']}")
        
        # 获取课程推荐
        query = {
            'type': 'curriculum_recommendation',
            'current_stage': scenario['game_state'].curriculum_stage,
            'student_performance': np.mean(scenario['performance_history']),
            'adversary_performance': scenario['game_state'].adversary_success_rate
        }
        
        recommendation = dataset_manager.provide_teacher_knowledge(query)
        
        print(f"     当前阶段: {scenario['game_state'].curriculum_stage}")
        print(f"     当前难度: {scenario['game_state'].difficulty_level:.3f}")
        print(f"     学生表现: {np.mean(scenario['performance_history']):.3f}")
        print(f"     对手成功率: {scenario['game_state'].adversary_success_rate:.3f}")
        print(f"     推荐行动: {recommendation['recommendation']}")
        print(f"     目标阶段: {recommendation['target_stage']}")
        print(f"     推荐理由: {recommendation['reasoning']}")


def demonstrate_complete_game_round():
    """演示完整的博弈轮次"""
    print("\n" + "="*60)
    print("🎮 完整博弈轮次演示")
    print("="*60)
    
    # 创建完整的博弈环境
    dataset_config = {'enable_dynamic_difficulty': True}
    dataset_manager = GameDatasetManager(dataset_config)
    dataset_manager.raw_dataset = create_demo_dataset(100)
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    # 创建博弈环境
    environment_config = {
        'noise_level': 0.01,
        'enable_teacher_intervention': True,
        'real_time_adaptation': True
    }
    
    game_env = GameEnvironment(dataset_manager, environment_config)
    
    # 模拟智能体策略
    teacher_strategy = {
        'curriculum_stage': 'foundation',
        'difficulty_level': 0.5,
        'intervention': {'type': 'hint_provision', 'intensity': 0.3},
        'feedback_frequency': 1.0
    }
    
    student_strategy = {
        'detection_threshold': 0.6,
        'feature_weights': torch.ones(10),
        'confidence_level': 0.8,
        'strategy_type': 'balanced'
    }
    
    adversary_strategy = {
        'anomaly_type': 'stealth',
        'intensity': 0.5,
        'target_features': [1, 3, 7],
        'evasion_technique': 'gradual'
    }
    
    # 创建博弈状态
    game_state = GameState(
        round_number=1,
        student_performance={'detection_accuracy': 0.7},
        adversary_success_rate=0.4,
        curriculum_stage='foundation',
        difficulty_level=0.5,
        historical_outcomes=[]
    )
    
    print("\n🎯 执行博弈轮次:")
    print(f"   教师策略: {teacher_strategy['curriculum_stage']} 阶段, 难度 {teacher_strategy['difficulty_level']}")
    print(f"   学生策略: 阈值 {student_strategy['detection_threshold']}, 类型 {student_strategy['strategy_type']}")
    print(f"   对手策略: {adversary_strategy['anomaly_type']} 攻击, 强度 {adversary_strategy['intensity']}")
    
    try:
        # 执行博弈轮次
        result = game_env.execute_round(
            teacher_strategy, student_strategy, adversary_strategy, game_state
        )
        
        print(f"\n📊 博弈结果:")
        print(f"   样本ID: {result.sample_id}")
        print(f"   学生成功: {result.student_success}")
        print(f"   对手成功: {result.adversary_success}")
        print(f"   检测准确率: {result.detection_accuracy:.3f}")
        print(f"   逃避率: {result.evasion_rate:.3f}")
        print(f"   博弈平衡: {result.game_balance:.3f}")
        print(f"   教师有效性: {result.teacher_effectiveness:.3f}")
        
        print(f"\n🔍 详细信息:")
        print(f"   原始样本难度: {result.original_sample.difficulty:.3f}")
        print(f"   原始样本领域: {result.original_sample.domain}")
        print(f"   学生置信度: {result.student_confidence:.3f}")
        print(f"   检测结果: {result.student_detection_result}")
        
    except Exception as e:
        print(f"   ❌ 博弈执行失败: {e}")


def visualize_dataset_roles():
    """可视化数据集角色"""
    print("\n" + "="*60)
    print("📊 数据集角色可视化")
    print("="*60)
    
    # 创建数据集统计
    dataset_config = {'enable_dynamic_difficulty': True}
    dataset_manager = GameDatasetManager(dataset_config)
    dataset_manager.raw_dataset = create_demo_dataset(500)
    dataset_manager._compute_sample_difficulties()
    dataset_manager._create_curriculum_partitions()
    
    # 获取统计信息
    stats = dataset_manager.get_dataset_statistics()
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 1. 难度分布
    difficulties = [s.difficulty for s in dataset_manager.raw_dataset]
    axes[0, 0].hist(difficulties, bins=20, alpha=0.7, color='skyblue')
    axes[0, 0].set_title('样本难度分布')
    axes[0, 0].set_xlabel('难度')
    axes[0, 0].set_ylabel('频次')
    
    # 2. 阶段分布
    stage_names = list(stats['partitions'].keys())
    stage_counts = [stats['partitions'][stage]['count'] for stage in stage_names]
    axes[0, 1].pie(stage_counts, labels=stage_names, autopct='%1.1f%%')
    axes[0, 1].set_title('课程阶段分布')
    
    # 3. 领域分布
    domains = {}
    for sample in dataset_manager.raw_dataset:
        domains[sample.domain] = domains.get(sample.domain, 0) + 1
    
    axes[1, 0].bar(domains.keys(), domains.values(), color=['lightcoral', 'lightgreen', 'lightsalmon'])
    axes[1, 0].set_title('领域分布')
    axes[1, 0].set_ylabel('样本数')
    
    # 4. 异常比例
    stage_anomaly_ratios = [stats['partitions'][stage]['anomaly_ratio'] for stage in stage_names]
    axes[1, 1].bar(stage_names, stage_anomaly_ratios, color='orange', alpha=0.7)
    axes[1, 1].set_title('各阶段异常比例')
    axes[1, 1].set_ylabel('异常比例')
    
    plt.tight_layout()
    plt.savefig('dataset_roles_visualization.png', dpi=300, bbox_inches='tight')
    print("📈 可视化图表已保存为 'dataset_roles_visualization.png'")
    
    # 打印统计摘要
    print(f"\n📊 数据集统计摘要:")
    print(f"   总样本数: {stats['total_samples']}")
    print(f"   平均难度: {np.mean(difficulties):.3f}")
    print(f"   难度标准差: {np.std(difficulties):.3f}")
    print(f"   领域数量: {len(domains)}")
    print(f"   整体异常比例: {sum(s.label for s in dataset_manager.raw_dataset) / len(dataset_manager.raw_dataset):.2%}")


def main():
    """主函数 - 运行所有演示"""
    print("🎓 数据集在博弈论异常检测框架中的角色演示")
    print("=" * 80)
    
    try:
        # 演示四个主要角色
        demonstrate_role_1_environment_infrastructure()
        demonstrate_role_2_teacher_knowledge_source()
        demonstrate_role_3_battleground()
        demonstrate_role_4_dynamic_difficulty_advisor()
        
        # 演示完整博弈轮次
        demonstrate_complete_game_round()
        
        # 可视化
        visualize_dataset_roles()
        
        print("\n" + "="*80)
        print("🎉 所有演示完成！")
        print("\n📝 总结:")
        print("   1. 数据集作为博弈环境基础设施，提供智能样本选择")
        print("   2. 数据集作为教师知识来源，支持课程设计和策略制定")
        print("   3. 数据集作为博弈战场，为学生和对手提供不对称信息")
        print("   4. 数据集作为难度顾问，提供动态调节建议")
        print("   5. 完整博弈轮次展示了数据集的综合作用")
        
        print("\n🚀 这种设计使数据集从被动资源转变为智能参与者！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
