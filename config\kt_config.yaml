# 难度推断配置
difficulty:
  k: 3                              # 难度等级数（1-3级）
  min_responses: 30                 # 知识点至少被30次作答才参与推断

# 规则参数配置
rules:
  # 规则1：连续答对后错误
  streak_threshold: 3               # 连续答对3次后错误视为异常
  
  # 规则2：知识掌握骤降
  drop_window: 5                    # 最近5次答题计算近期正确率
  drop_threshold: 0.3               # 历史正确率比近期高30%视为异常
  
  # 规则3：低效率重复
  repeat_threshold: 5               # 同一知识点答题≥5次
  low_acc_threshold: 0.4            # 正确率<40%视为低效率
  
  # 规则4：全对/全错序列
  same_window: 8                    # 连续8次全对/全错
  min_skills: 3                     # 涉及至少3个知识点
  
  # 规则5：知识点跳跃
  mastery_threshold: 0.7            # 前置知识点正确率≥70%才算掌握
  
  # 规则6：异常序列长度
  sigma: 2.0                        # 超过2倍标准差视为异常
  
  # 融合参数
  fusion_threshold: 0.3        # 总异常分≥0.6标记为异常

# 规则权重（和需≈1）
rule_weights: [0.3, 0.3, 0.2, 0.1, 0.1, 0.0]

# 并行配置
parallel:
  workers: 4                        # 使用4个进程并行处理
