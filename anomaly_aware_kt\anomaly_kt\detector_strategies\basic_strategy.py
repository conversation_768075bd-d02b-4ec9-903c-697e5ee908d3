"""
Basic异常检测器训练策略

基础策略使用标准的训练流程：
- 标准BCE损失函数
- 固定的异常比例
- 简单的数据增强
- Adam优化器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Any

from .base_strategy import BaseDetectorStrategy


class BasicDetectorStrategy(BaseDetectorStrategy):
    """Basic异常检测器训练策略"""
    
    def _get_strategy_config(self) -> Dict:
        """获取Basic策略配置"""
        return {
            'strategy_type': 'basic',
            'loss_function': 'bce',
            'optimizer': 'adam',
            'scheduler': 'step_lr',
            'anomaly_ratio': 'fixed',
            'data_augmentation': 'simple',
            'class_weights': 'balanced'
        }
        
    def _create_optimizer(self, learning_rate: float) -> torch.optim.Optimizer:
        """创建Adam优化器"""
        return torch.optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-5,
            betas=(0.9, 0.999)
        )
        
    def _create_scheduler(self, optimizer: torch.optim.Optimizer, epochs: int) -> Optional[Any]:
        """创建StepLR调度器"""
        return torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=epochs // 3,  # 每1/3轮数降低学习率
            gamma=0.5
        )
        
    def _calculate_loss(self, outputs: torch.Tensor, targets: torch.Tensor,
                       epoch: int, total_epochs: int) -> torch.Tensor:
        """计算BCE损失"""
        # 计算类别权重
        pos_weight = self._calculate_class_weights(targets)
        
        # BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(
            outputs, targets.float(), pos_weight=pos_weight
        )
        
        return bce_loss
        
    def _calculate_class_weights(self, targets: torch.Tensor) -> torch.Tensor:
        """计算类别权重"""
        # 计算正负样本比例
        pos_count = targets.sum().item()
        neg_count = (targets == 0).sum().item()
        
        if pos_count == 0:
            return torch.tensor(1.0, device=self.device)
            
        # 平衡权重
        pos_weight = neg_count / pos_count
        return torch.tensor(pos_weight, device=self.device)
        
    def _adjust_training_params(self, epoch: int, total_epochs: int,
                               current_performance: Dict) -> Dict:
        """调整训练参数 - Basic策略保持参数固定"""
        return {
            'anomaly_ratio': 0.1,  # 固定异常比例
            'grad_clip': 1.0,      # 梯度裁剪
        }
        
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "Basic Strategy"
        
    def get_detailed_info(self) -> Dict:
        """获取详细信息"""
        return {
            'strategy_name': self.get_strategy_name(),
            'description': 'Basic异常检测器训练策略',
            'features': [
                '标准BCE损失函数',
                '固定异常比例 (10%)',
                'Adam优化器',
                'StepLR学习率调度',
                '平衡类别权重',
                '简单梯度裁剪'
            ],
            'advantages': [
                '实现简单，易于理解',
                '训练稳定，收敛快',
                '计算开销小',
                '适合快速原型开发'
            ],
            'limitations': [
                '无法处理复杂的类别不平衡',
                '缺乏自适应能力',
                '对数据质量要求较高',
                '性能上限相对较低'
            ],
            'best_use_cases': [
                '快速基准测试',
                '数据相对平衡的场景',
                '计算资源有限的环境',
                '原型验证阶段'
            ],
            'hyperparameters': {
                'learning_rate': '1e-3 (推荐)',
                'weight_decay': '1e-5',
                'anomaly_ratio': '0.1 (固定)',
                'grad_clip': '1.0',
                'scheduler_step_size': 'epochs // 3',
                'scheduler_gamma': '0.5'
            }
        }
        
    def validate_hyperparameters(self, **kwargs) -> Dict:
        """验证超参数"""
        recommendations = {}
        
        # 检查学习率
        lr = kwargs.get('learning_rate', 1e-3)
        if lr > 1e-2:
            recommendations['learning_rate'] = '学习率过高，建议使用1e-3到1e-4之间'
        elif lr < 1e-5:
            recommendations['learning_rate'] = '学习率过低，可能导致收敛缓慢'
            
        # 检查异常比例
        anomaly_ratio = kwargs.get('anomaly_ratio', 0.1)
        if anomaly_ratio > 0.3:
            recommendations['anomaly_ratio'] = 'Basic策略不适合处理高异常比例，建议使用Enhanced或Aggressive策略'
            
        # 检查训练轮数
        epochs = kwargs.get('epochs', 30)
        if epochs < 10:
            recommendations['epochs'] = '训练轮数过少，建议至少20轮'
        elif epochs > 100:
            recommendations['epochs'] = 'Basic策略通常在50轮内收敛，过多轮数可能导致过拟合'
            
        return recommendations
        
    def get_training_tips(self) -> Dict:
        """获取训练建议"""
        return {
            'data_preparation': [
                '确保数据质量，移除明显的噪声',
                '检查类别分布，避免极端不平衡',
                '考虑数据标准化或归一化'
            ],
            'hyperparameter_tuning': [
                '从默认学习率1e-3开始',
                '如果收敛慢，可以尝试1e-4',
                '如果不稳定，可以尝试5e-4',
                '监控验证损失，避免过拟合'
            ],
            'monitoring': [
                '关注F1分数和AUC指标',
                '检查训练和验证损失的差距',
                '观察精确率和召回率的平衡',
                '使用早停避免过拟合'
            ],
            'troubleshooting': [
                '如果精确率低：减少异常比例或增加正则化',
                '如果召回率低：增加异常比例或调整类别权重',
                '如果训练不稳定：降低学习率或增加批次大小',
                '如果收敛慢：检查数据质量或尝试不同的初始化'
            ]
        }
