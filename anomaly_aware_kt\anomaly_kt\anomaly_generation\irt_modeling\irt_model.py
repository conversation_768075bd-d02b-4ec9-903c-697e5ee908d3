"""
IRT难度建模核心实现

基于项目反应理论(Item Response Theory)的难度建模，支持：
1. 1PL模型 (Rasch模型) - 只考虑难度参数
2. 2PL模型 - 考虑难度和区分度参数  
3. 3PL模型 - 考虑难度、区分度和猜测参数

理论基础：
P(θ, a, b, c) = c + (1-c) * exp(a*(θ-b)) / (1 + exp(a*(θ-b)))
其中：θ为能力参数，a为区分度，b为难度，c为猜测参数
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import warnings


class IRTModel(Enum):
    """IRT模型类型"""
    RASCH = "1PL"      # 1参数Logistic模型 (Rasch模型)
    TWO_PL = "2PL"     # 2参数Logistic模型
    THREE_PL = "3PL"   # 3参数Logistic模型


@dataclass
class IRTParameters:
    """IRT参数"""
    difficulty: torch.Tensor    # 难度参数 (b)
    discrimination: Optional[torch.Tensor] = None  # 区分度参数 (a)
    guessing: Optional[torch.Tensor] = None        # 猜测参数 (c)
    
    def to_dict(self) -> Dict[str, torch.Tensor]:
        """转换为字典格式"""
        result = {'difficulty': self.difficulty}
        if self.discrimination is not None:
            result['discrimination'] = self.discrimination
        if self.guessing is not None:
            result['guessing'] = self.guessing
        return result


class IRTDifficultyModel:
    """
    IRT难度建模核心类
    
    提供基于项目反应理论的难度建模功能，支持多种IRT模型。
    """
    
    def __init__(self, 
                 model_type: IRTModel = IRTModel.TWO_PL,
                 device: str = 'cpu'):
        """
        初始化IRT难度模型
        
        Args:
            model_type: IRT模型类型
            device: 计算设备
        """
        self.model_type = model_type
        self.device = device
        self.parameters = None
        self.is_fitted = False
        
        # 模型参数约束
        self.constraints = {
            'difficulty': (-4.0, 4.0),      # 难度参数范围
            'discrimination': (0.1, 3.0),   # 区分度参数范围
            'guessing': (0.0, 0.3)          # 猜测参数范围
        }
        
    def probability(self, 
                   ability: torch.Tensor,
                   item_params: IRTParameters) -> torch.Tensor:
        """
        计算IRT概率函数
        
        Args:
            ability: 能力参数 θ [batch_size] 或 [batch_size, seq_len]
            item_params: 项目参数
            
        Returns:
            probability: 正确回答概率 [batch_size, n_items] 或 [batch_size, seq_len, n_items]
        """
        difficulty = item_params.difficulty.to(self.device)
        
        # 确保维度匹配
        if ability.dim() == 1:  # [batch_size]
            ability = ability.unsqueeze(-1)  # [batch_size, 1]
        if ability.dim() == 2 and difficulty.dim() == 1:
            difficulty = difficulty.unsqueeze(0)  # [1, n_items]
            
        # 计算能力与难度的差值
        theta_minus_b = ability.unsqueeze(-1) - difficulty.unsqueeze(0)
        
        if self.model_type == IRTModel.RASCH:
            # 1PL模型: P = exp(θ-b) / (1 + exp(θ-b))
            prob = torch.sigmoid(theta_minus_b)
            
        elif self.model_type == IRTModel.TWO_PL:
            # 2PL模型: P = exp(a*(θ-b)) / (1 + exp(a*(θ-b)))
            discrimination = item_params.discrimination.to(self.device)
            if discrimination.dim() == 1:
                discrimination = discrimination.unsqueeze(0)
            logit = discrimination.unsqueeze(0) * theta_minus_b
            prob = torch.sigmoid(logit)
            
        elif self.model_type == IRTModel.THREE_PL:
            # 3PL模型: P = c + (1-c) * exp(a*(θ-b)) / (1 + exp(a*(θ-b)))
            discrimination = item_params.discrimination.to(self.device)
            guessing = item_params.guessing.to(self.device)
            
            if discrimination.dim() == 1:
                discrimination = discrimination.unsqueeze(0)
            if guessing.dim() == 1:
                guessing = guessing.unsqueeze(0)
                
            logit = discrimination.unsqueeze(0) * theta_minus_b
            prob_2pl = torch.sigmoid(logit)
            prob = guessing.unsqueeze(0) + (1 - guessing.unsqueeze(0)) * prob_2pl
            
        else:
            raise ValueError(f"Unsupported IRT model: {self.model_type}")
            
        return prob.squeeze()
        
    def log_likelihood(self,
                      responses: torch.Tensor,
                      ability: torch.Tensor,
                      item_params: IRTParameters) -> torch.Tensor:
        """
        计算对数似然函数
        
        Args:
            responses: 响应矩阵 [batch_size, n_items]
            ability: 能力参数 [batch_size]
            item_params: 项目参数
            
        Returns:
            log_likelihood: 对数似然值 [batch_size]
        """
        # 计算响应概率
        prob = self.probability(ability, item_params)
        
        # 避免数值问题
        prob = torch.clamp(prob, 1e-8, 1-1e-8)
        
        # 计算对数似然
        log_prob = responses * torch.log(prob) + (1 - responses) * torch.log(1 - prob)
        
        # 只考虑有效响应（非负值）
        valid_mask = responses >= 0
        log_likelihood = (log_prob * valid_mask).sum(dim=-1)
        
        return log_likelihood
        
    def estimate_ability(self,
                        responses: torch.Tensor,
                        item_params: IRTParameters,
                        prior_mean: float = 0.0,
                        prior_std: float = 1.0,
                        max_iter: int = 50,
                        tolerance: float = 1e-6) -> torch.Tensor:
        """
        估计能力参数 (Maximum A Posteriori)
        
        Args:
            responses: 响应矩阵 [batch_size, n_items]
            item_params: 项目参数
            prior_mean: 先验均值
            prior_std: 先验标准差
            max_iter: 最大迭代次数
            tolerance: 收敛容差
            
        Returns:
            ability: 估计的能力参数 [batch_size]
        """
        batch_size = responses.shape[0]
        
        # 初始化能力参数
        ability = torch.zeros(batch_size, device=self.device)
        
        for iteration in range(max_iter):
            old_ability = ability.clone()
            
            # 计算一阶和二阶导数
            prob = self.probability(ability, item_params)
            prob = torch.clamp(prob, 1e-8, 1-1e-8)
            
            # 有效响应掩码
            valid_mask = responses >= 0
            
            # 一阶导数 (梯度)
            if self.model_type == IRTModel.RASCH:
                gradient = ((responses - prob) * valid_mask).sum(dim=-1)
                # 加入先验
                gradient -= (ability - prior_mean) / (prior_std ** 2)
                
                # 二阶导数 (Hessian)
                hessian = -(prob * (1 - prob) * valid_mask).sum(dim=-1)
                hessian -= 1 / (prior_std ** 2)
                
            elif self.model_type in [IRTModel.TWO_PL, IRTModel.THREE_PL]:
                discrimination = item_params.discrimination.to(self.device)
                
                gradient = ((responses - prob) * discrimination * valid_mask).sum(dim=-1)
                gradient -= (ability - prior_mean) / (prior_std ** 2)
                
                hessian = -(prob * (1 - prob) * (discrimination ** 2) * valid_mask).sum(dim=-1)
                hessian -= 1 / (prior_std ** 2)
                
            # Newton-Raphson更新
            # 避免除零
            hessian = torch.clamp(hessian, min=-1e6, max=-1e-6)
            ability = ability - gradient / hessian
            
            # 约束能力参数范围
            ability = torch.clamp(ability, -4.0, 4.0)
            
            # 检查收敛
            if torch.max(torch.abs(ability - old_ability)) < tolerance:
                break
                
        return ability
        
    def estimate_difficulty_simple(self, responses: torch.Tensor) -> torch.Tensor:
        """
        简单难度估计（基于正确率）
        
        Args:
            responses: 响应矩阵 [batch_size, n_items]
            
        Returns:
            difficulty: 难度参数 [n_items]
        """
        # 计算每个项目的正确率
        valid_mask = responses >= 0
        correct_rate = (responses * valid_mask).sum(dim=0) / valid_mask.sum(dim=0).clamp(min=1)
        
        # 避免极端值
        correct_rate = torch.clamp(correct_rate, 0.01, 0.99)
        
        # 转换为难度参数: b = -log(p/(1-p))
        difficulty = -torch.log(correct_rate / (1 - correct_rate))
        
        return difficulty
        
    def calibrate_items(self,
                       responses: torch.Tensor,
                       max_iter: int = 100,
                       tolerance: float = 1e-4) -> IRTParameters:
        """
        校准项目参数
        
        Args:
            responses: 响应矩阵 [batch_size, n_items]
            max_iter: 最大迭代次数
            tolerance: 收敛容差
            
        Returns:
            item_params: 校准后的项目参数
        """
        batch_size, n_items = responses.shape
        
        # 初始化参数
        difficulty = self.estimate_difficulty_simple(responses)
        
        if self.model_type in [IRTModel.TWO_PL, IRTModel.THREE_PL]:
            discrimination = torch.ones(n_items, device=self.device)
        else:
            discrimination = None
            
        if self.model_type == IRTModel.THREE_PL:
            guessing = torch.full((n_items,), 0.1, device=self.device)
        else:
            guessing = None
            
        # 迭代优化（简化的EM算法）
        for iteration in range(max_iter):
            old_difficulty = difficulty.clone()
            
            # E步：估计能力参数
            current_params = IRTParameters(difficulty, discrimination, guessing)
            ability = self.estimate_ability(responses, current_params)
            
            # M步：更新项目参数
            difficulty = self._update_difficulty(responses, ability, current_params)
            
            if self.model_type in [IRTModel.TWO_PL, IRTModel.THREE_PL]:
                discrimination = self._update_discrimination(responses, ability, difficulty)
                
            if self.model_type == IRTModel.THREE_PL:
                guessing = self._update_guessing(responses, ability, difficulty, discrimination)
                
            # 检查收敛
            if torch.max(torch.abs(difficulty - old_difficulty)) < tolerance:
                break
                
        # 应用约束
        difficulty = torch.clamp(difficulty, *self.constraints['difficulty'])
        if discrimination is not None:
            discrimination = torch.clamp(discrimination, *self.constraints['discrimination'])
        if guessing is not None:
            guessing = torch.clamp(guessing, *self.constraints['guessing'])
            
        self.parameters = IRTParameters(difficulty, discrimination, guessing)
        self.is_fitted = True
        
        return self.parameters
        
    def _update_difficulty(self,
                          responses: torch.Tensor,
                          ability: torch.Tensor,
                          current_params: IRTParameters) -> torch.Tensor:
        """更新难度参数"""
        n_items = responses.shape[1]
        new_difficulty = torch.zeros(n_items, device=self.device)
        
        for item in range(n_items):
            item_responses = responses[:, item]
            valid_mask = item_responses >= 0
            
            if valid_mask.sum() == 0:
                continue
                
            valid_responses = item_responses[valid_mask]
            valid_ability = ability[valid_mask]
            
            # 使用Newton-Raphson方法更新难度
            b = current_params.difficulty[item]
            
            for _ in range(10):  # 内层迭代
                if self.model_type == IRTModel.RASCH:
                    prob = torch.sigmoid(valid_ability - b)
                else:
                    a = current_params.discrimination[item]
                    prob = torch.sigmoid(a * (valid_ability - b))
                    
                prob = torch.clamp(prob, 1e-8, 1-1e-8)
                
                if self.model_type == IRTModel.RASCH:
                    gradient = (valid_responses - prob).sum()
                    hessian = -(prob * (1 - prob)).sum()
                else:
                    a = current_params.discrimination[item]
                    gradient = (a * (valid_responses - prob)).sum()
                    hessian = -(a**2 * prob * (1 - prob)).sum()
                    
                if abs(hessian) < 1e-6:
                    break
                    
                b = b - gradient / hessian
                
            new_difficulty[item] = b
            
        return new_difficulty
        
    def _update_discrimination(self,
                             responses: torch.Tensor,
                             ability: torch.Tensor,
                             difficulty: torch.Tensor) -> torch.Tensor:
        """更新区分度参数"""
        n_items = responses.shape[1]
        new_discrimination = torch.ones(n_items, device=self.device)
        
        for item in range(n_items):
            item_responses = responses[:, item]
            valid_mask = item_responses >= 0
            
            if valid_mask.sum() == 0:
                continue
                
            valid_responses = item_responses[valid_mask]
            valid_ability = ability[valid_mask]
            b = difficulty[item]
            
            # 简化更新：基于相关性
            theta_minus_b = valid_ability - b
            correlation = torch.corrcoef(torch.stack([valid_responses.float(), theta_minus_b]))[0, 1]
            
            if not torch.isnan(correlation):
                new_discrimination[item] = max(0.1, min(3.0, abs(correlation) * 2))
                
        return new_discrimination
        
    def _update_guessing(self,
                        responses: torch.Tensor,
                        ability: torch.Tensor,
                        difficulty: torch.Tensor,
                        discrimination: torch.Tensor) -> torch.Tensor:
        """更新猜测参数"""
        n_items = responses.shape[1]
        new_guessing = torch.zeros(n_items, device=self.device)
        
        for item in range(n_items):
            item_responses = responses[:, item]
            valid_mask = item_responses >= 0
            
            if valid_mask.sum() == 0:
                continue
                
            valid_responses = item_responses[valid_mask]
            valid_ability = ability[valid_mask]
            
            # 找到低能力学生的正确率作为猜测参数的估计
            low_ability_mask = valid_ability < -1.0
            if low_ability_mask.sum() > 0:
                low_ability_correct_rate = valid_responses[low_ability_mask].float().mean()
                new_guessing[item] = min(0.3, max(0.0, low_ability_correct_rate.item()))
                
        return new_guessing
        
    def get_item_information(self, 
                           ability: torch.Tensor,
                           item_params: IRTParameters) -> torch.Tensor:
        """
        计算项目信息函数
        
        Args:
            ability: 能力参数
            item_params: 项目参数
            
        Returns:
            information: 项目信息 [len(ability), n_items]
        """
        prob = self.probability(ability, item_params)
        
        if self.model_type == IRTModel.RASCH:
            information = prob * (1 - prob)
        elif self.model_type in [IRTModel.TWO_PL, IRTModel.THREE_PL]:
            discrimination = item_params.discrimination
            if self.model_type == IRTModel.THREE_PL:
                guessing = item_params.guessing
                information = (discrimination ** 2) * ((prob - guessing) ** 2) / (prob * (1 - prob))
            else:
                information = (discrimination ** 2) * prob * (1 - prob)
                
        return information
        
    def predict_difficulty(self, 
                          new_responses: torch.Tensor,
                          method: str = 'map') -> torch.Tensor:
        """
        预测新项目的难度
        
        Args:
            new_responses: 新项目的响应数据
            method: 预测方法 ('map', 'simple')
            
        Returns:
            predicted_difficulty: 预测的难度参数
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        if method == 'simple':
            return self.estimate_difficulty_simple(new_responses)
        elif method == 'map':
            # 使用已有参数作为先验进行MAP估计
            return self._map_difficulty_estimation(new_responses)
        else:
            raise ValueError(f"Unknown method: {method}")
            
    def _map_difficulty_estimation(self, responses: torch.Tensor) -> torch.Tensor:
        """MAP难度估计"""
        # 简化实现：使用现有参数的均值作为先验
        prior_mean = self.parameters.difficulty.mean()
        prior_std = self.parameters.difficulty.std()
        
        # 基于简单估计进行调整
        simple_difficulty = self.estimate_difficulty_simple(responses)
        
        # 贝叶斯更新（简化）
        n_responses = (responses >= 0).sum(dim=0)
        weight = n_responses / (n_responses + 1)  # 简化的权重
        
        map_difficulty = weight * simple_difficulty + (1 - weight) * prior_mean
        
        return map_difficulty
